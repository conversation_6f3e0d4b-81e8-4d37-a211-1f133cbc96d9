<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Valorar Tour"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Tour Info -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:src="@android:drawable/ic_menu_mapmode"
                        android:background="@drawable/circle_primary"
                        android:tint="@color/white"
                        android:padding="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_tour_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="City Tour Lima Centro"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <TextView
                            android:id="@+id/tv_company_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Lima Adventure Tours"
                            android:textSize="14sp"
                            android:textColor="@color/gray"
                            android:layout_marginTop="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="✅ Tour completado"
                            android:textSize="12sp"
                            android:textColor="@color/green"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Rating Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¿Cómo calificarías este tour?"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Star Rating -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:id="@+id/star_1"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:text="⭐"
                            android:textSize="32sp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="?attr/selectableItemBackgroundBorderless" />

                        <TextView
                            android:id="@+id/star_2"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:text="⭐"
                            android:textSize="32sp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="?attr/selectableItemBackgroundBorderless" />

                        <TextView
                            android:id="@+id/star_3"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:text="⭐"
                            android:textSize="32sp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="?attr/selectableItemBackgroundBorderless" />

                        <TextView
                            android:id="@+id/star_4"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:text="⭐"
                            android:textSize="32sp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="?attr/selectableItemBackgroundBorderless" />

                        <TextView
                            android:id="@+id/star_5"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:text="⭐"
                            android:textSize="32sp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="?attr/selectableItemBackgroundBorderless" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_rating_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Selecciona tu calificación"
                        android:textSize="14sp"
                        android:textColor="@color/gray" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Comment Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Comparte tu experiencia"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Escribe tu comentario (opcional)"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_comment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minLines="3"
                            android:maxLines="5"
                            android:gravity="top"
                            android:inputType="textMultiLine|textCapSentences" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Submit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_submit_rating"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Enviar Valoración"
                android:textSize="16sp"
                android:paddingVertical="12dp"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>