<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- ================= TOOLBAR ================= -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Reportes"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"/>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- ================= CONTENT ================= -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- ================= KPI HEADER ================= -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="14dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center">

                <!-- Ventas -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_total_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 0"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ingresos"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="1dp"
                    android:layout_height="36dp"
                    android:background="#E0E0E0" />

                <!-- Reservas -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_total_reservations"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Reservas"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="1dp"
                    android:layout_height="36dp"
                    android:background="#E0E0E0" />

                <!-- Ticket -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_avg_ticket"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 0"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ticket Prom."
                        android:textSize="12sp"
                        android:textColor="@color/gray" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- ================= FILTERS ================= -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="8dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Período"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C" />

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none"
                    android:layout_marginTop="8dp">

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_period"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:singleSelection="true"
                        app:chipSpacingHorizontal="8dp">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_daily"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Diario"
                            android:checkable="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_monthly"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mensual"
                            android:checkable="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_annual"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Anual"
                            android:checkable="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_general"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="General"
                            android:checkable="true"
                            android:checked="true" />
                    </com.google.android.material.chip.ChipGroup>
                </HorizontalScrollView>

                <!-- Selectores de fecha según el período -->
                <LinearLayout
                    android:id="@+id/date_selection_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="12dp"
                    android:visibility="gone">

                    <!-- Selector para Diario (calendario) -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_select_date"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:text="Seleccionar día"
                        android:textColor="@color/primary"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        app:icon="@android:drawable/ic_menu_today"
                        app:iconTint="@color/primary"
                        app:iconGravity="textStart"
                        app:iconPadding="12dp"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        app:strokeColor="@color/primary"
                        app:strokeWidth="2dp"
                        app:cornerRadius="12dp" />

                    <!-- Selectores para Mensual (mes y año) -->
                    <LinearLayout
                        android:id="@+id/month_selection_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_month"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Mes"
                            android:minHeight="56dp"
                            app:boxCornerRadiusTopStart="12dp"
                            app:boxCornerRadiusTopEnd="12dp"
                            app:boxCornerRadiusBottomStart="12dp"
                            app:boxCornerRadiusBottomEnd="12dp"
                            app:boxStrokeWidth="2dp"
                            app:boxStrokeWidthFocused="2dp">

                            <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                                android:id="@+id/spinner_month"
                                android:layout_width="match_parent"
                                android:layout_height="56dp"
                                android:inputType="none"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                android:paddingVertical="16dp" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_year"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Año"
                            android:minHeight="56dp"
                            android:layout_marginStart="12dp"
                            app:boxCornerRadiusTopStart="12dp"
                            app:boxCornerRadiusTopEnd="12dp"
                            app:boxCornerRadiusBottomStart="12dp"
                            app:boxCornerRadiusBottomEnd="12dp"
                            app:boxStrokeWidth="2dp"
                            app:boxStrokeWidthFocused="2dp">

                            <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                                android:id="@+id/spinner_year"
                                android:layout_width="match_parent"
                                android:layout_height="56dp"
                                android:inputType="none"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                android:paddingVertical="16dp" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- Selector para Anual (solo año) -->
                    <LinearLayout
                        android:id="@+id/year_selection_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_year_only"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Año"
                            android:minHeight="56dp"
                            app:boxCornerRadiusTopStart="12dp"
                            app:boxCornerRadiusTopEnd="12dp"
                            app:boxCornerRadiusBottomStart="12dp"
                            app:boxCornerRadiusBottomEnd="12dp"
                            app:boxStrokeWidth="2dp"
                            app:boxStrokeWidthFocused="2dp">

                            <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                                android:id="@+id/spinner_year_only"
                                android:layout_width="match_parent"
                                android:layout_height="56dp"
                                android:inputType="none"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                android:paddingVertical="16dp" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- ================= TABS ================= -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:tabIndicatorColor="@color/primary"
            app:tabSelectedTextColor="@color/primary"
            app:tabTextColor="@color/gray">

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Servicios" />

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tours" />

            <com.google.android.material.tabs.TabItem
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Resumen" />
        </com.google.android.material.tabs.TabLayout>

        <!-- ================= CONTENT ================= -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
