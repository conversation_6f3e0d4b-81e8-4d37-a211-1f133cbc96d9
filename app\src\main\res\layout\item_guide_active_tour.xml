<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Status Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/primary"
            android:padding="12dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_mapmode"
                android:tint="@color/white" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_tour_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="EN PROGRESO"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/tv_tour_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Punto 2 de 4"
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_payment_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 180"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/white" />

        </LinearLayout>

        <!-- Tour Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Tour Title -->
            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="City Tour Lima Centro Histórico"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="12dp" />

            <!-- Tour Details -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📅 Fecha"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Hoy, 15 Dic"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏰ Hora"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="09:00 - 13:00"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="👥 Personas"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_participants_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="6 personas"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Current Location -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/bg_location_current"
                android:padding="12dp"
                android:layout_marginBottom="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@android:drawable/ic_menu_mylocation"
                    android:tint="@color/primary" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="12dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ubicación Actual"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_current_location"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Plaza de Armas"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_arrival_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:15 AM"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:id="@+id/layout_tour_actions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_view_map"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ver Mapa"
                    android:drawableStart="@android:drawable/ic_menu_mapmode"
                    android:layout_marginEnd="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_register_location"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Registrar Llegada"
                    android:layout_marginEnd="8dp" />

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/fab_scan_qr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:contentDescription="Escanear QR"
                    app:srcCompat="@android:drawable/ic_menu_camera"
                    app:backgroundTint="@color/primary"
                    app:tint="@color/white"
                    app:fabCustomSize="44dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
