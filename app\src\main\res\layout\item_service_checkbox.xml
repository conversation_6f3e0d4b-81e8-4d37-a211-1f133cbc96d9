<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingVertical="4dp">

    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/cb_service"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="15sp"
        android:textColor="#2C2C2C"
        android:paddingVertical="8dp"
        app:buttonTint="@color/primary" />

    <TextView
        android:id="@+id/tv_service_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="#4CAF50"
        android:textStyle="bold"
        android:paddingEnd="8dp" />

</LinearLayout>

