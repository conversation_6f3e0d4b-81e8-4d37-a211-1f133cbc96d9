<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/light_gray">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="DroidTour"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:menu="@menu/top_app_bar_admin"
            app:navigationIcon="@android:drawable/ic_menu_sort_alphabetically" />

        <!-- SwipeRefreshLayout envolviendo el contenido -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Scrollable Content -->
            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="80dp">

                <!-- Welcome Card mejorado -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_welcome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="@color/primary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:gravity="center_vertical">

                        <!-- Avatar del admin -->
                        <FrameLayout
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:layout_marginEnd="16dp">

                            <View
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:background="@drawable/circle_white"
                                android:alpha="0.2" />

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_greeting"
                                app:tint="@color/white" />
                        </FrameLayout>

                        <!-- Información del admin -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_welcome_admin"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="¡Hola, Admin!"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/white" />

                            <TextView
                                android:id="@+id/tv_company_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Empresa de Tours"
                                android:textSize="14sp"
                                android:textColor="@color/white"
                                android:alpha="0.9"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <!-- Contadores en columna vertical -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="start">

                            <!-- Contador de alertas -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="8dp">

                                <TextView
                                    android:id="@+id/tv_pending_alerts_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="3"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/white"
                                    android:background="@drawable/circle_orange"
                                    android:gravity="center"
                                    android:minWidth="32dp"
                                    android:minHeight="32dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Alertas"
                                    android:textSize="12sp"
                                    android:textColor="@color/white"
                                    android:alpha="0.9"
                                    android:layout_marginStart="8dp" />
                            </LinearLayout>

                            <!-- Contador de chats activos -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <TextView
                                    android:id="@+id/tv_active_chats_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="5"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/white"
                                    android:background="@drawable/circle_blue"
                                    android:gravity="center"
                                    android:minWidth="32dp"
                                    android:minHeight="32dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Chats"
                                    android:textSize="12sp"
                                    android:textColor="@color/white"
                                    android:alpha="0.9"
                                    android:layout_marginStart="8dp" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Sección: Acciones Rápidas -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="24dp"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Acciones Rápidas"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <!-- Alertas -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_alerts"
                            android:layout_width="0dp"
                            android:layout_height="110dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="12dp">

                                <FrameLayout
                                    android:layout_width="56dp"
                                    android:layout_height="56dp"
                                    android:layout_marginBottom="12dp">

                                    <View
                                        android:layout_width="56dp"
                                        android:layout_height="56dp"
                                        android:background="@drawable/bg_alert_orange" />

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:layout_gravity="center"
                                        android:src="@android:drawable/ic_dialog_alert"
                                        app:tint="#FFFFFF" />
                                </FrameLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Alertas"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:textAlignment="center" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <!-- Mis Chats -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_customer_chat"
                            android:layout_width="0dp"
                            android:layout_height="110dp"
                            android:layout_weight="1"
                            android:layout_marginHorizontal="4dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="12dp">

                                <FrameLayout
                                    android:layout_width="56dp"
                                    android:layout_height="56dp"
                                    android:layout_marginBottom="12dp">

                                    <View
                                        android:layout_width="56dp"
                                        android:layout_height="56dp"
                                        android:background="@drawable/bg_chat_blue" />

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:layout_gravity="center"
                                        android:src="@drawable/ic_chat_neo"
                                        app:tint="#FFFFFF" />
                                </FrameLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Mis Chats"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:textAlignment="center" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <!-- Reportes -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_reports"
                            android:layout_width="0dp"
                            android:layout_height="110dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="12dp">

                                <FrameLayout
                                    android:layout_width="56dp"
                                    android:layout_height="56dp"
                                    android:layout_marginBottom="12dp">

                                    <View
                                        android:layout_width="56dp"
                                        android:layout_height="56dp"
                                        android:background="@drawable/bg_report_green" />

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:layout_gravity="center"
                                        android:src="@drawable/ic_doc_filled"
                                        app:tint="#FFFFFF" />
                                </FrameLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Reportes"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:textAlignment="center" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </LinearLayout>

                <!-- Sección: Gestión de Tours -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="24dp"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Gestión de Tours"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Crear Tour -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_create_tour"
                            android:layout_width="0dp"
                            android:layout_height="110dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="12dp">

                                <FrameLayout
                                    android:layout_width="56dp"
                                    android:layout_height="56dp"
                                    android:layout_marginBottom="12dp">

                                    <View
                                        android:layout_width="56dp"
                                        android:layout_height="56dp"
                                        android:background="@drawable/bg_icon_purple" />

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:layout_gravity="center"
                                        android:src="@android:drawable/ic_input_add"
                                        app:tint="#FFFFFF" />
                                </FrameLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Tours"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:textAlignment="center" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <!-- Crear Servicio -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_create_service"
                            android:layout_width="0dp"
                            android:layout_height="110dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="12dp">

                                <FrameLayout
                                    android:layout_width="56dp"
                                    android:layout_height="56dp"
                                    android:layout_marginBottom="12dp">

                                    <View
                                        android:layout_width="56dp"
                                        android:layout_height="56dp"
                                        android:background="@drawable/bg_icon_orange" />

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:layout_gravity="center"
                                        android:src="@android:drawable/ic_menu_add"
                                        app:tint="#FFFFFF" />
                                </FrameLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Servicios"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:textAlignment="center" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </LinearLayout>

                <!-- Sección: Gestión de Guías -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="24dp"
                    android:paddingHorizontal="16dp"
                    android:paddingBottom="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Gestión de Guías"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- Gestión de Guías -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_guide_management"
                            android:layout_width="0dp"
                            android:layout_height="110dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="12dp">

                                <FrameLayout
                                    android:layout_width="56dp"
                                    android:layout_height="56dp"
                                    android:layout_marginBottom="12dp">

                                    <View
                                        android:layout_width="56dp"
                                        android:layout_height="56dp"
                                        android:background="@drawable/bg_icon_gray" />

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:layout_gravity="center"
                                        android:src="@android:drawable/ic_menu_manage"
                                        app:tint="#FFFFFF" />
                                </FrameLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Gestión de Guías"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:textAlignment="center" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <!-- Seguimiento de Guías -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_guide_tracking"
                            android:layout_width="0dp"
                            android:layout_height="110dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="12dp">

                                <FrameLayout
                                    android:layout_width="56dp"
                                    android:layout_height="56dp"
                                    android:layout_marginBottom="12dp">

                                    <View
                                        android:layout_width="56dp"
                                        android:layout_height="56dp"
                                        android:background="@drawable/bg_icon_brown" />

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:layout_gravity="center"
                                        android:src="@android:drawable/ic_menu_mylocation"
                                        app:tint="#FFFFFF" />
                                </FrameLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Seguimiento"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:textAlignment="center" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="280dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:headerLayout="@layout/nav_header_admin"
        app:menu="@menu/tour_admin_nav_menu"
        app:itemShapeFillColor="@color/nav_active_indicator"/>

    <!-- FAB para crear tour -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_create_tour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="24dp"
        app:srcCompat="@android:drawable/ic_input_add"
        app:tint="@color/white"
        app:backgroundTint="@color/primary"
        app:elevation="8dp" />

</androidx.drawerlayout.widget.DrawerLayout>