<?xml version="1.0" encoding="utf-8"?>
<!-- Archivo: res/layout/notification_action_layout.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="?attr/actionBarSize"
    android:layout_height="?attr/actionBarSize"
    android:background="?attr/selectableItemBackgroundBorderless"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:id="@+id/iv_notification_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center"
        android:src="@drawable/ic_notificaciones_24"
        android:tint="@color/white"
        android:contentDescription="Notificaciones" />

    <!-- Badge de contador -->
    <TextView
        android:id="@+id/tv_notification_badge"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center"
        android:layout_marginStart="12dp"
        android:layout_marginBottom="12dp"
        android:gravity="center"
        android:text="3"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        android:visibility="visible" />

</FrameLayout>