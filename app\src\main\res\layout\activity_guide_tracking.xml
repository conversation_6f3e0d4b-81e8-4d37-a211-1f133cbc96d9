<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Seguimiento de Guías"
        app:titleTextColor="@color/white"
        app:titleCentered="true"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:navigationIcon="@drawable/ic_back"
        app:navigationIconTint="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- ✅ Map inside a Card (mejor look y constraints correctos) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_map_container"
        android:layout_width="0dp"
        android:layout_height="0dp"



        android:layout_marginBottom="12dp"
        app:cardCornerRadius="0dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/white"
        app:strokeWidth="1dp"
        app:strokeColor="#ECECEC"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/card_active_guides"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Map -->
            <fragment
                android:id="@+id/map_fragment"
                android:name="com.google.android.gms.maps.SupportMapFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <!-- Subtle overlay (opcional, no afecta funcionalidad) -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#08000000" />

        </FrameLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Active Guides List -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_active_guides"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/white"
        app:strokeWidth="1dp"
        app:strokeColor="#ECECEC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.32">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Header (azul suave + burbuja icono) -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="14dp">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@drawable/ic_guide"
                    android:layout_marginEnd="7dp"
                    app:tint="@color/primary"
                    android:contentDescription="Mapa" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Guías Activos"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#202124" />

                <TextView
                    android:id="@+id/tv_active_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 activos"
                    android:textSize="12sp"
                    android:textColor="#6B7280" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#EEEEEE" />

            <!-- Guides List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_active_guides"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:paddingBottom="12dp"
                android:clipToPadding="false"
                android:overScrollMode="never" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
