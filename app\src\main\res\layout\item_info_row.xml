<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="10dp"
    android:gravity="center_vertical">

    <!-- Etiqueta -->
    <TextView
        android:id="@+id/tv_label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Label"
        android:textColor="#757575"
        android:textSize="14sp" />

    <!-- Valor -->
    <TextView
        android:id="@+id/tv_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Valor"
        android:textStyle="bold"
        android:textSize="14sp"
        android:textColor="#2C2C2C"/>
</LinearLayout>
