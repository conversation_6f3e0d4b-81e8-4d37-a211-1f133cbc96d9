<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- AppBarLayout -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Reservar Tour"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="80dp">

            <!-- Header informativo -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@color/primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_confirmation"
                        app:tint="@color/white"
                        android:contentDescription="Reserva" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Confirmar tu Reserva"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Completa los detalles para finalizar"
                            android:textSize="14sp"
                            android:textColor="#E8E8E8"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Sección: Información del Tour -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_map_location"
                            app:tint="#2196F3"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Tour" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Información del Tour"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Detalles del tour -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#F9F9F9"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:paddingVertical="16dp">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@android:drawable/ic_menu_mapmode"
                            android:background="@drawable/circle_primary"
                            android:tint="@color/white"
                            android:padding="12dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_tour_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="City Tour Lima Centro"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/tv_company_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Lima Adventure Tours"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Detalles de la Reserva -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_calendar_check"
                            app:tint="#9C27B0"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Detalles" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Detalles de la Reserva"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Fecha del tour (solo lectura) -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@drawable/bg_detail_item"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_calendar_form"
                            android:tint="@color/primary"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Fecha" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Fecha del Tour"
                                android:textSize="13sp"
                                android:textColor="#757575" />

                            <TextView
                                android:id="@+id/tv_tour_date_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="15 Diciembre, 2024 - 09:00"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Método de Pago -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_payment"
                            app:tint="#E91E63"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Pago" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Método de Pago"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Selector de método de pago -->
                    <LinearLayout
                        android:id="@+id/layout_payment_method"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="16dp"
                        android:layout_marginBottom="8dp">

                        <ImageView
                            android:id="@+id/iv_card_icon"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_card_form"
                            android:tint="@color/primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_payment_method_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Seleccionar tarjeta"
                                android:textSize="16sp"
                                android:textColor="@color/black"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_payment_method_info"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Toca para elegir"
                                android:textSize="14sp"
                                android:textColor="@color/gray"
                                android:layout_marginTop="2dp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_chevron_right"
                            android:tint="@color/gray" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Selecciona o agrega un método de pago para continuar"
                        android:textSize="13sp"
                        android:textColor="#757575"
                        android:layout_marginTop="4dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Resumen de Pago -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_receipt_24"
                            app:tint="#4CAF50"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Resumen" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Resumen de Pago"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Detalles del pago -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Precio base del tour:"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C" />

                        <TextView
                            android:id="@+id/tv_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="S/. 55.00"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Precio de servicios:"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C" />

                        <TextView
                            android:id="@+id/tv_service_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="S/. 0.00"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="4dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Total a pagar:"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <TextView
                            android:id="@+id/tv_total_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="S/. 170.00"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/green" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Botón Confirmar Reserva -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_confirm_booking"
                style="@style/Widget.Material3.Button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Confirmar Reserva"
                android:textSize="16sp"
                android:textStyle="bold"
                app:icon="@drawable/ic_check_circle"
                app:iconTint="@color/white"
                app:iconSize="24dp"
                app:iconGravity="textStart"
                app:cornerRadius="12dp"
                app:backgroundTint="@color/primary"
                android:textColor="@color/white"
                android:layout_marginHorizontal="4dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>