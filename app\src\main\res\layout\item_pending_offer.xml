<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header with Company Logo and Status -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/light_gray"
            android:padding="12dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@android:drawable/ic_menu_info_details"
                android:tint="@color/primary"
                android:background="@drawable/circle_white"
                android:padding="6dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="10dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lima Adventure"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_offer_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Hace 2 horas"
                    android:textSize="11sp"
                    android:textColor="@color/gray" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="PENDIENTE"
                android:textSize="10sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/circle_orange"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp" />

        </LinearLayout>

        <!-- Tour Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="City Tour Centro Histórico"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="12dp" />

            <!-- Date and Time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📅 Fecha"
                        android:textSize="11sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="15 Dic, 2024"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏰ Hora"
                        android:textSize="11sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="09:00 AM"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Payment and Participants -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/bg_tour_info"
                android:padding="12dp"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💰 Pago"
                        android:textSize="11sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_payment_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 180"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/green"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="end">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="👥 Personas"
                        android:textSize="11sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_participants"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="8 pax"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_reject"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Rechazar"
                    android:textColor="@color/red"
                    android:textSize="12sp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_accept"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Aceptar"
                    android:textSize="12sp"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

