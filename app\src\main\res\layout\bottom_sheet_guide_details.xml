<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_bottom_sheet_rounded"
    android:clipToPadding="false"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <!-- Handle del bottom sheet -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bottom_sheet_handle"
        android:backgroundTint="#E0E0E0" />

    <!-- Header con botón cerrar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingHorizontal="20dp"
        android:paddingTop="8dp"
        android:paddingBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Detalles del Guía"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#2C2C2C" />

        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:tint="#757575"
            android:contentDescription="Cerrar" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="600dp"
        android:fillViewport="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="20dp"
            android:paddingBottom="20dp">

            <!-- Sección: Avatar y datos básicos -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Avatar y nombre -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            app:cardCornerRadius="32dp"
                            app:cardElevation="0dp"
                            app:strokeWidth="2dp"
                            app:strokeColor="@color/primary">

                            <FrameLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/primary_light">

                                <ImageView
                                    android:id="@+id/iv_guide_avatar"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:contentDescription="Avatar"
                                    tools:src="@drawable/ic_avatar_24" />

                                <TextView
                                    android:id="@+id/tv_avatar_initial"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/primary"
                                    tools:text="CG" />
                            </FrameLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:layout_marginStart="16dp">

                            <TextView
                                android:id="@+id/tv_guide_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Carlos González"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C"
                                android:maxLines="2"
                                android:ellipsize="end"
                                tools:text="Carlos González" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chip_guide_role"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Guía Turístico"
                                android:textSize="12sp"
                                android:layout_marginTop="4dp"
                                app:chipBackgroundColor="@color/primary_light"
                                android:textColor="@color/primary"
                                app:chipStrokeWidth="0dp"
                                app:chipMinHeight="28dp"
                                app:chipCornerRadius="8dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Divisor -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <!-- Detalles del guía -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Email -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_email"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Email" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Email"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <TextView
                                android:id="@+id/tv_guide_email"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="<EMAIL>"
                                android:textSize="14sp"
                                android:textColor="#2C2C2C"
                                android:textStyle="bold"
                                android:maxLines="1"
                                android:ellipsize="end"
                                tools:text="<EMAIL>" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#F5F5F5" />

                        <!-- Teléfono -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_phone"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Teléfono" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Teléfono"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <TextView
                                android:id="@+id/tv_guide_phone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="+51 999 999 999"
                                android:textSize="14sp"
                                android:textColor="#2C2C2C"
                                android:textStyle="bold"
                                tools:text="+51 987 654 321" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#F5F5F5" />

                        <!-- Fecha de registro -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_calendar_form"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Fecha" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Miembro desde"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <TextView
                                android:id="@+id/tv_registration_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="15 Dic 2024"
                                android:textSize="14sp"
                                android:textColor="#2C2C2C"
                                android:textStyle="bold"
                                tools:text="Nov 2024" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#F5F5F5" />

                        <!-- Estado -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_info"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Estado" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Estado"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chip_guide_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Activo"
                                android:textSize="12sp"
                                app:chipBackgroundColor="#E8F5E9"
                                android:textColor="#4CAF50"
                                app:chipStrokeWidth="0dp"
                                app:chipMinHeight="28dp"
                                app:chipCornerRadius="8dp"
                                tools:text="Activo" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Idiomas -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_languages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_language"
                            android:tint="#FF9800"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Idiomas" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Idiomas"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <!-- ChipGroup de idiomas -->
                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_languages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:chipSpacingHorizontal="8dp"
                        app:chipSpacingVertical="8dp"
                        app:singleLine="false" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Biografía (si existe) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_biography"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_info"
                            android:tint="#2196F3"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Biografía" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Sobre mí"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_biography"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Biografía del guía..."
                        android:textSize="14sp"
                        android:textColor="#424242"
                        android:lineSpacingMultiplier="1.3"
                        tools:text="Guía turístico profesional con más de 5 años de experiencia..." />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Estadísticas -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_statistics"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_stats"
                            android:tint="#4CAF50"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Estadísticas" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Estadísticas"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <!-- Stats grid -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:baselineAligned="false">

                        <!-- Tours -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tv_tours_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="#2196F3"
                                tools:text="12" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tours"
                                android:textSize="13sp"
                                android:textColor="#757575"
                                android:gravity="center"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <!-- Rating -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tv_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0.0"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="#FF9800"
                                tools:text="4.8" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Rating"
                                android:textSize="13sp"
                                android:textColor="#757575"
                                android:gravity="center"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <!-- Experiencia -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tv_experience_years"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="#9C27B0"
                                tools:text="5" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Años"
                                android:textSize="13sp"
                                android:textColor="#757575"
                                android:gravity="center"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Especialidades (si existe) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_specialties"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_star"
                            android:tint="#FFC107"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Especialidades" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Especialidades"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_specialties"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Especialidades del guía..."
                        android:textSize="14sp"
                        android:textColor="#424242"
                        tools:text="Historia, Arqueología, Gastronomía" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
