<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white">



    <!-- CONTENIDO EXPANDIDO -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/expanded_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <!-- <PERSON>le del bottom sheet -->
            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bottom_sheet_handle"
                android:backgroundTint="#E0E0E0" />

            <!-- Header-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="20dp"
                android:paddingBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Ubicaciones del Tour"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C" />

                <ImageButton
                    android:id="@+id/btn_close_sheet"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_close"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Cerrar"
                    app:tint="#9E9E9E" />
            </LinearLayout>

            <!-- Lista de ubicaciones -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_map_location"
                            app:tint="#9C27B0"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Lista" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Paradas en Orden"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <!-- RecyclerView para las ubicaciones -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_locations"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:padding="4dp"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_tour_location_neo" />

                    <!-- Mensaje cuando no hay ubicaciones -->
                    <LinearLayout
                        android:id="@+id/layout_empty_locations"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_empty_box"
                            app:tint="#BDBDBD"
                            android:layout_marginBottom="12dp"
                            android:contentDescription="Sin ubicaciones" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="No hay paradas agregadas"
                            android:textSize="14sp"
                            android:textColor="#757575" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Toca el mapa para agregar la primera parada"
                            android:textSize="12sp"
                            android:textColor="#9E9E9E"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Información del tour -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_location"
                            app:tint="#4CAF50"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Ubicaciones" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Paradas del Recorrido"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Total de paradas"
                            android:textSize="14sp"
                            android:textColor="#757575" />

                        <TextView
                            android:id="@+id/tv_total_stops"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2196F3"
                            tools:text="5" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#F5F5F5" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Distancia total"
                            android:textSize="14sp"
                            android:textColor="#757575" />

                        <TextView
                            android:id="@+id/tv_total_distance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 km"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#4CAF50"
                            tools:text="45.2 km" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Botones de acción -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="20dp"
                android:paddingTop="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_clear_all"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Limpiar todo"
                        android:textSize="14sp"
                        app:icon="@drawable/ic_trash"
                        app:iconTint="#F44336"
                        app:iconSize="18dp"
                        app:iconGravity="textStart"
                        app:cornerRadius="8dp"
                        app:strokeColor="#F44336"
                        android:textColor="#F44336"
                        android:layout_marginEnd="8dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_save_route"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Guardar"
                        android:textSize="14sp"
                        app:icon="@drawable/ic_check_circle"
                        app:iconSize="18dp"
                        app:iconGravity="textStart"
                        app:cornerRadius="8dp"
                        app:backgroundTint="@color/primary" />
                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_optimize_route"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Optimizar ruta automáticamente"
                    android:textSize="13sp"
                    app:icon="@drawable/ic_route"
                    app:iconSize="16dp"
                    app:iconGravity="textStart"
                    android:textColor="#2196F3"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"/>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>