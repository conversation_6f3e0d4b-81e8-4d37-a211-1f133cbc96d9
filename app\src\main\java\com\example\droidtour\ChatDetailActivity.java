package com.example.droidtour;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ChatDetailActivity extends AppCompatActivity {

    private TextView tvClientName, tvClientStatus, tvTourName, tvTourDate, tvBookingStatus;
    private ImageView ivClientAvatar, ivCallClient;
    private RecyclerView rvMessages;
    private TextInputEditText etMessage;
    private FloatingActionButton fabSendMessage;

    private String chatId, companyName, tourName;
    private ChatMessagesAdapter messagesAdapter;
    private List<ChatMessage> messagesList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chat_detail);
        
        getIntentData();
        setupToolbar();
        initializeViews();
        setupClickListeners();
        setupRecyclerView();
        loadChatData();
    }
    
    private void getIntentData() {
        chatId = getIntent().getStringExtra("CHAT_ID");
        companyName = getIntent().getStringExtra("COMPANY_NAME");
        tourName = getIntent().getStringExtra("TOUR_NAME");

        // Para compatibilidad con llamadas desde CustomerChatActivity
        if (companyName == null) {
            companyName = getIntent().getStringExtra("CLIENT_NAME");
        }
    }
    
    private void setupToolbar() {
        MaterialToolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
    }
    
    private void initializeViews() {
        tvClientName = findViewById(R.id.tv_client_name);
        tvClientStatus = findViewById(R.id.tv_client_status);
        tvTourName = findViewById(R.id.tv_tour_name);
        tvTourDate = findViewById(R.id.tv_tour_date);
        tvBookingStatus = findViewById(R.id.tv_booking_status);
        
        ivClientAvatar = findViewById(R.id.iv_client_avatar);
        ivCallClient = findViewById(R.id.iv_call_client);
        
        rvMessages = findViewById(R.id.rv_messages);
        etMessage = findViewById(R.id.et_message);
        fabSendMessage = findViewById(R.id.fab_send_message);
    }
    
    private void setupClickListeners() {
        ivCallClient.setOnClickListener(v -> {
            Toast.makeText(this, "Llamar a " + companyName, Toast.LENGTH_SHORT).show();
            // TODO: Implementar llamada telefónica
        });
        
        fabSendMessage.setOnClickListener(v -> sendMessage());
    }
    
    private void setupRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setStackFromEnd(true); // Para mostrar mensajes más recientes abajo
        rvMessages.setLayoutManager(layoutManager);

        messagesList = new ArrayList<>();
        messagesAdapter = new ChatMessagesAdapter(messagesList);
        rvMessages.setAdapter(messagesAdapter);

        loadSampleMessages();
    }
    
    private void loadChatData() {
        // TODO: Cargar datos reales desde base de datos
        // Por ahora mostrar datos de prueba
        if (companyName != null) {
            tvClientName.setText(companyName);
        }
        if (tourName != null) {
            tvTourName.setText(tourName);
        }

        tvClientStatus.setText("En línea");
        tvTourDate.setText("Mañana, 15 Dic • 09:00 AM");
        tvBookingStatus.setText("CONFIRMADO");
    }

    private void loadSampleMessages() {
        // Mensajes de ejemplo basados en la imagen proporcionada
        messagesList.add(new ChatMessage("Hola, buenas!", false, "14:20"));
        messagesList.add(new ChatMessage("Me interesa comprar el top verde", false, "14:21"));
        messagesList.add(new ChatMessage("¡Hola, buenas tardes!", true, "14:22"));
        messagesList.add(new ChatMessage("Sí, claro. ¿Cómo pagará?", true, "14:23"));
        messagesList.add(new ChatMessage("Con tarjeta.", false, "14:24"));
        messagesList.add(new ChatMessage("¿Es seguro? nunca he comprado por esta app.", false, "14:25"));
        messagesList.add(new ChatMessage("Sí, es seguro", true, "14:26"));
        messagesList.add(new ChatMessage("La app tiene un sistema de protección contra robos y estafas", true, "14:27"));
        messagesList.add(new ChatMessage("Perfecto! ¿Cómo es el envío?", false, "14:28"));
        messagesList.add(new ChatMessage("Usted solo realiza la transacción y yo le enviaré un motorizado con un costo adicional de 5 soles.", true, "14:29"));

        messagesAdapter.notifyDataSetChanged();
        rvMessages.scrollToPosition(messagesList.size() - 1);
    }
    
    private void sendMessage() {
        String messageText = etMessage.getText().toString().trim();

        if (messageText.isEmpty()) {
            Toast.makeText(this, "Escriba un mensaje", Toast.LENGTH_SHORT).show();
            return;
        }

        // Agregar mensaje a la lista
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
        String currentTime = timeFormat.format(new Date());

        ChatMessage newMessage = new ChatMessage(messageText, false, currentTime); // false = mensaje del cliente
        messagesList.add(newMessage);
        messagesAdapter.notifyItemInserted(messagesList.size() - 1);
        rvMessages.scrollToPosition(messagesList.size() - 1);

        etMessage.setText("");

        // TODO: Enviar mensaje a la base de datos
        Toast.makeText(this, "Mensaje enviado", Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}

// Clase para representar un mensaje de chat
class ChatMessage {
    public String message;
    public boolean isFromCompany; // true = empresa, false = cliente
    public String timestamp;

    public ChatMessage(String message, boolean isFromCompany, String timestamp) {
        this.message = message;
        this.isFromCompany = isFromCompany;
        this.timestamp = timestamp;
    }
}

// Adaptador para los mensajes del chat
class ChatMessagesAdapter extends RecyclerView.Adapter<ChatMessagesAdapter.MessageViewHolder> {
    private List<ChatMessage> messages;

    public ChatMessagesAdapter(List<ChatMessage> messages) {
        this.messages = messages;
    }

    @NonNull
    @Override
    public MessageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_chat_message, parent, false);
        return new MessageViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MessageViewHolder holder, int position) {
        ChatMessage message = messages.get(position);

        if (message.isFromCompany) {
            // Mensaje de la empresa (mostrar como mensaje de empresa en el layout)
            holder.layoutIncoming.setVisibility(View.VISIBLE);
            holder.layoutOutgoing.setVisibility(View.GONE);

            holder.tvIncomingMessage.setText(message.message);
            if (holder.tvIncomingTime != null) {
                holder.tvIncomingTime.setText(message.timestamp);
            }
        } else {
            // Mensaje del cliente (mostrar como mensaje de usuario en el layout)
            holder.layoutIncoming.setVisibility(View.GONE);
            holder.layoutOutgoing.setVisibility(View.VISIBLE);

            holder.tvOutgoingMessage.setText(message.message);
            // No hay campo de tiempo para mensajes salientes en este layout
        }
    }

    @Override
    public int getItemCount() {
        return messages.size();
    }

    static class MessageViewHolder extends RecyclerView.ViewHolder {
        LinearLayout layoutIncoming, layoutOutgoing, layoutSystem;
        TextView tvIncomingMessage, tvIncomingTime;
        TextView tvOutgoingMessage, tvOutgoingTime;
        TextView tvSystemMessage;

        public MessageViewHolder(@NonNull View itemView) {
            super(itemView);

            layoutIncoming = itemView.findViewById(R.id.layout_company_message);
            layoutOutgoing = itemView.findViewById(R.id.layout_user_message);
            // No hay layout_system_message en el layout actual
            layoutSystem = null;

            tvIncomingMessage = itemView.findViewById(R.id.tv_company_message);
            // ID correcto para el tiempo de mensaje de la empresa
            tvIncomingTime = itemView.findViewById(R.id.tv_company_message_time);

            tvOutgoingMessage = itemView.findViewById(R.id.tv_user_message);
            // Campo de tiempo para mensaje de usuario (saliente)
            tvOutgoingTime = itemView.findViewById(R.id.tv_user_message_time);

            // No hay tv_system_message en el layout actual
            tvSystemMessage = null;
        }
    }
}
