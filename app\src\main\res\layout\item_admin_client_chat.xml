<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="14dp"
        android:gravity="center_vertical"
        android:background="@color/white">

        <!-- ================= AVATAR ================= -->
        <FrameLayout
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginEnd="14dp">

            <!-- Placeholder -->
            <View
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:background="@drawable/circle_light_gray" />

            <!-- Imagen real del usuario -->
            <ImageView
                android:id="@+id/img_client_avatar"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_avatar_24"
                android:background="@drawable/profile_image_circle"
                android:scaleType="centerCrop" />

            <!-- (Opcional futuro) Indicador online -->
            <!--
            <View
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_gravity="bottom|end"
                android:background="@drawable/circle_online"
                android:layout_margin="4dp" />
            -->
        </FrameLayout>

        <!-- ================= CHAT CONTENT ================= -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Header: Nombre + Hora -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_client_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="María González"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_timestamp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2:30 PM"
                    android:textSize="12sp"
                    android:textColor="#9E9E9E" />
            </LinearLayout>

            <!-- Último mensaje -->
            <TextView
                android:id="@+id/tv_last_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Hola, tengo una pregunta sobre el tour que reservé para mañana"
                android:textSize="14sp"
                android:textColor="#757575"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- ================= UNREAD BADGE ================= -->
        <TextView
            android:id="@+id/tv_unread_count"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/circle_primary"
            android:gravity="center"
            android:text="2"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
