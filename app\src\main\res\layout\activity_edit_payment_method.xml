<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Editar Tarjeta"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleCentered="true"
        app:titleTextColor="@color/white"
        app:navigationIcon="?attr/homeAsUpIndicator"
        app:navigationIconTint="@color/white"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Header Info -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="#FFF3E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_edit"
                        app:tint="#FF6F00" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Edita el nombre del titular y la fecha de vencimiento"
                        android:textSize="14sp"
                        android:textColor="@color/black" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Card Holder Name -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_card_holder"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Nombre del titular"
                app:startIconDrawable="@drawable/ic_user_form"
                app:startIconTint="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_card_holder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textCapCharacters"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Expiry Date Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp">

                <!-- Expiry Month -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_expiry_month"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:hint="Mes (MM)">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_expiry_month"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textSize="16sp" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Expiry Year -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_expiry_year"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:hint="Año (YY)">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_expiry_year"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textSize="16sp" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <!-- Security Notice -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="#E8F5E9">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        app:tint="#388E3C" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="12dp"
                        android:text="ℹNo puedes cambiar el número de tarjeta por seguridad"
                        android:textSize="12sp"
                        android:textColor="#388E3C" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Update Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_update_card"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Actualizar Tarjeta"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                android:backgroundTint="@color/primary"
                android:textColor="@color/white"
                app:icon="@drawable/ic_check_circle"
                app:iconGravity="textStart" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>

