<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Header -->
        <TextView
            android:id="@+id/tv_register_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/register"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Role Selection -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_role_selection"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/tv_register_title">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/role_selection"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/dark_gray"
                    android:layout_marginBottom="12dp" />

                <RadioGroup
                    android:id="@+id/rg_user_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <RadioButton
                        android:id="@+id/rb_client"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/client"
                        android:checked="true" />

                    <RadioButton
                        android:id="@+id/rb_tour_guide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tour_guide" />

                    <RadioButton
                        android:id="@+id/rb_tour_admin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/tour_admin" />

                </RadioGroup>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- User Information Form -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_user_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintTop_toBottomOf="@id/card_role_selection">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/name">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPersonName" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_last_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/last_name">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_last_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPersonName" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/email">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/phone">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/password"
                    app:endIconMode="password_toggle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_confirm_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/confirm_password"
                    app:endIconMode="password_toggle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_confirm_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_register"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="24dp"
                    android:text="@string/register"
                    android:textSize="16sp"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Login Link -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/card_user_info"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¿Ya tienes cuenta? "
                android:textColor="@color/dark_gray" />

            <TextView
                android:id="@+id/tv_login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/login"
                android:textColor="@color/primary"
                android:textStyle="bold"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
