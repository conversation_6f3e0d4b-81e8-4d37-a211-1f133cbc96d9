<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            android:elevation="4dp">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/primary"
                app:title="Recuperación"
                app:titleTextAppearance="@style/Toolbar.TitleText3"
                app:titleCentered="true"
                app:titleTextColor="@color/white"
                app:navigationIcon="?attr/homeAsUpIndicator"
                app:navigationIconTint="@color/white" />

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Icono de contraseña -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="24dp"
                android:src="@drawable/ic_password"
                android:tint="@color/primary"
                android:contentDescription="Icono de seguridad" />

            <!-- Título -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="¿Olvidaste tu contraseña?"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <!-- Descripción -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Ingresa tu correo electrónico y te enviaremos un enlace para restablecer tu contraseña."
                android:textSize="14sp"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="32dp" />

            <!-- Campo de correo -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Correo Electrónico"
                app:startIconDrawable="@drawable/ic_email"
                app:startIconTint="@color/primary"
                app:endIconMode="clear_text"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress"
                    android:maxLines="1"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Mensaje informativo -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="#FFF3E0"
                app:cardElevation="0dp"
                app:cardCornerRadius="8dp"
                app:strokeWidth="1dp"
                app:strokeColor="#FFB300">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        android:tint="#F57C00"
                        android:layout_marginEnd="12dp"
                        android:contentDescription="Información" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Nota: Esta opción solo está disponible para cuentas registradas con correo electrónico. Las cuentas de Google deben recuperarse desde su plataforma."
                        android:textSize="13sp"
                        android:textColor="#E65100"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Mensaje de éxito (inicialmente oculto) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_success"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:visibility="gone"
                app:cardBackgroundColor="#E8F5E9"
                app:cardElevation="2dp"
                app:cardCornerRadius="8dp"
                app:strokeWidth="1dp"
                app:strokeColor="#4CAF50">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#2E7D32"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Éxito" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="¡Enlace enviado!"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#1B5E20" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_success_message"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Hemos enviado un enlace de recuperación a tu correo electrónico. Por favor revisa tu bandeja de entrada y sigue las instrucciones."
                        android:textSize="14sp"
                        android:textColor="#2E7D32"
                        android:lineSpacingExtra="2dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Si no recibes el correo en unos minutos, revisa tu carpeta de spam."
                        android:textSize="12sp"
                        android:textColor="#558B2F"
                        android:textStyle="italic" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Botón enviar -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_send_link"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:text="Enviar Enlace de Recuperación"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="8dp"
                app:elevation="2dp"
                android:layout_marginBottom="16dp"
                app:backgroundTint="@color/primary"
                android:textColor="@color/white"
                />

            <!-- Botón cancelar -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Cancelar"
                android:textSize="14sp"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                app:strokeColor="@color/primary"
                app:strokeWidth="1dp"
                app:cornerRadius="8dp"
                app:backgroundTint="@color/white"
                android:textColor="@color/primary"
                />

        </LinearLayout>

    </LinearLayout>

</ScrollView>