<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FE003B95"
    android:padding="24dp">

    <!-- Botón Saltar - Posición fija arriba derecha -->
    <TextView
        android:id="@+id/saltar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/bricolage_grotesque"
        android:text="@string/saltar"
        android:textAlignment="center"
        android:textAllCaps="false"
        android:textColor="#FFFFFF"
        android:textStyle="bold"
        android:textSize="16sp"
        android:padding="8dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Contenedor principal centrado -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/saltar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="2dp">

        <!-- Mensaje 1 - Título principal -->
        <TextView
            android:id="@+id/mensaje1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/bricolage_grotesque"
            android:text="@string/mensaje1"
            android:textColor="#FFFFFF"
            android:textSize="40sp"
            android:textStyle="bold"
            android:textAlignment="center"
            android:lineSpacingExtra="2dp"
            android:layout_marginHorizontal="24dp"
            android:maxLines="3"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/imageninit"
            app:layout_constraintVertical_chainStyle="packed" />

        <!-- Imagen principal -->
        <ImageView
            android:id="@+id/imageninit"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/saludo_init"
            android:scaleType="fitCenter"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:contentDescription="Imagen de bienvenida"
            app:layout_constraintWidth_max="280dp"
            app:layout_constraintHeight_max="320dp"
            app:layout_constraintDimensionRatio="H,4:5"
            app:layout_constraintTop_toBottomOf="@id/mensaje1"
            app:layout_constraintBottom_toTopOf="@id/mensaje2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintVertical_bias="0.4" />

        <!-- Mensaje 2 - Descripción -->
        <TextView
            android:id="@+id/mensaje2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/bricolage_grotesque"
            android:text="@string/mensaje2"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="22sp"
            android:textStyle="bold"
            android:lineSpacingExtra="4dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="32dp"
            app:layout_constraintTop_toBottomOf="@id/imageninit"
            app:layout_constraintBottom_toTopOf="@id/button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Botón siguiente -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/button"
            style="@style/Widget.Material3.Button.ElevatedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Siguiente"
            android:textColor="#2196F3"
            android:textSize="16sp"
            android:textStyle="bold"
            android:paddingHorizontal="32dp"
            android:paddingVertical="12dp"
            android:backgroundTint="@color/white"
            app:cornerRadius="24dp"
            app:elevation="4dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="16dp"
            app:icon="@drawable/ic_chevron_right"
            app:iconGravity="end"
            app:iconTint="#2196F3"
            app:layout_constraintTop_toBottomOf="@id/mensaje2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>