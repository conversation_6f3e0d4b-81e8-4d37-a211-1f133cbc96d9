<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Empresas de Turismo"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Scrollable Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Search Card - Diseño Simplificado con Iconos Integrados -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@color/white">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Buscar empresa o destino..."
                    app:startIconDrawable="@android:drawable/ic_menu_search"
                    app:startIconTint="@color/gray"
                    app:endIconMode="clear_text"
                    app:endIconTint="@color/gray"
                    app:boxBackgroundMode="none"
                    app:boxBackgroundColor="@android:color/transparent"
                    app:hintTextColor="@color/gray"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:padding="8dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionSearch"
                        android:maxLines="1"
                        android:background="@android:color/transparent"
                        android:textColor="@color/black"
                        android:textColorHint="@color/gray"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Filter Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                android:paddingVertical="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Filtrar por:"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:singleLine="false"
                    app:chipSpacingHorizontal="8dp"
                    app:chipSpacingVertical="8dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_all"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Todas"
                        android:checkable="true"
                        android:checked="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_best_rated"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Mejor Valoradas"
                        android:checkable="true"

                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_most_tours"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Más Tours"
                        android:checkable="true"

                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_best_price"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Mejor Precio"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                </com.google.android.material.chip.ChipGroup>

            </LinearLayout>

            <!-- Companies List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_companies"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingBottom="16dp"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>