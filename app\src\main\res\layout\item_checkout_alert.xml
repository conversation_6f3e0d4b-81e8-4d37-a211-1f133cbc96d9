<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with Client and Tour Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_agenda"
                android:tint="@color/primary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_client_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ana García Pérez"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="City Tour Lima Centro"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_checkout_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hace 5 min"
                android:textSize="12sp"
                android:textColor="@color/gray" />

        </LinearLayout>

        <!-- Payment Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Monto a Cobrar"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="S/. 85.00"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/green"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Método de Pago"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="4dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@android:drawable/ic_menu_help"
                        android:tint="@color/primary" />

                    <TextView
                        android:id="@+id/tv_payment_method"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="**** 4532"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:layout_marginStart="6dp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Estado"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_payment_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="PENDIENTE"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/orange"
                    android:background="@drawable/bg_status_pending"
                    android:padding="6dp"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/layout_pending_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:visibility="visible">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_details"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ver Detalles"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_process_payment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Procesar Pago" />

        </LinearLayout>

        <!-- Processed Status (for completed payments) -->
        <LinearLayout
            android:id="@+id/layout_processed_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@android:drawable/ic_menu_agenda"
                android:tint="@color/green" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Pago procesado exitosamente"
                android:textSize="14sp"
                android:textColor="@color/green"
                android:textStyle="bold"
                android:layout_marginStart="8dp" />

            <TextView
                android:id="@+id/tv_processed_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="14:30"
                android:textSize="12sp"
                android:textColor="@color/gray" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
