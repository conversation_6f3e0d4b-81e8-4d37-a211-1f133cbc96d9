<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Mis Reservas"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Scrollable Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Summary Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_summary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_calendar_check"
                        app:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_total_reservations"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total de reservas: 8"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:id="@+id/tv_active_reservations"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Activas: 2 • Completadas: 6"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_total_spent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 680"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/white" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Filter Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="12dp"
                android:paddingVertical="8dp">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:singleLine="false"
                    app:chipSpacingHorizontal="8dp"
                    app:chipSpacingVertical="8dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_all"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Todas"
                        android:checkable="true"
                        android:checked="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_active"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Activas"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_completed"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Completadas"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_cancelled"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Canceladas"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                </com.google.android.material.chip.ChipGroup>

            </LinearLayout>

            <!-- Reservations List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_reservations"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingBottom="16dp"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>

