<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Detalles de la Oferta"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Estado Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_offer_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="PENDIENTE"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/white"
                        android:background="@drawable/circle_orange"
                        android:paddingStart="20dp"
                        android:paddingEnd="20dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_offer_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Enviado hace 2 horas"
                        android:textSize="13sp"
                        android:textColor="@color/gray"
                        android:layout_marginTop="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Tour Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📍 Información del Tour"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="City Tour Lima Centro Histórico"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_company_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Lima Tours SAC"
                        android:textSize="15sp"
                        android:textColor="@color/gray"
                        android:layout_marginBottom="16dp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/light_gray"
                        android:layout_marginBottom="16dp" />

                    <!-- Fecha y Hora -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📅 Fecha"
                                android:textSize="13sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_tour_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="2024-12-20"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⏰ Hora"
                                android:textSize="13sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_tour_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="09:00"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Duración y Participantes -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⏱️ Duración"
                                android:textSize="13sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_tour_duration"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="4 horas"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="👥 Participantes"
                                android:textSize="13sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_participants"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="6 personas"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Pago Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💰 Pago Ofrecido"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_payment_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 180.00"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        android:textColor="@color/green" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Idiomas requeridos (si aplica) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_languages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🗣️ Idiomas requeridos"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_languages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:chipSpacingHorizontal="8dp"
                        app:chipSpacingVertical="8dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Mapa del Recorrido -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_map"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🗺️ Recorrido del Tour"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <!-- Vista de mapa simplificada -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="12dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:id="@+id/iv_tour_map"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:src="@drawable/map_placeholder"
                            android:contentDescription="Mapa del recorrido" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="#11000000" />

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_location"
                            android:tint="@color/primary"
                            android:contentDescription="Icono de ubicación" />

                    </androidx.cardview.widget.CardView>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_view_full_map"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Ver Mapa Completo"
                        android:textColor="@color/primary"
                        app:strokeColor="@color/primary"
                        app:icon="@drawable/ic_location"
                        app:iconTint="@color/primary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Notas Adicionales (si hay) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_notes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📝 Notas de la Empresa"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_additional_notes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Sin notas adicionales"
                        android:textSize="15sp"
                        android:textColor="@color/text_primary"
                        android:background="@drawable/rounded_background_light"
                        android:padding="12dp"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Botones de acción (solo para ofertas pendientes) -->
            <LinearLayout
                android:id="@+id/layout_action_buttons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="8dp"
                android:visibility="visible">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_reject"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Rechazar"
                    android:textColor="@color/red"
                    app:strokeColor="@color/red"
                    android:layout_marginEnd="8dp"
                    android:padding="14dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_accept"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Aceptar Oferta"
                    android:backgroundTint="@color/primary"
                    android:textColor="@color/white"
                    android:layout_marginStart="8dp"
                    android:padding="14dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

