<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/light_gray">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="DroidTour"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:menu="@menu/top_app_bar_general"
            app:navigationIcon="@android:drawable/ic_menu_sort_alphabetically" />

        <!-- Scrollable Content -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Welcome Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_welcome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="12dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/primary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:src="@drawable/ic_greeting"
                            app:tint="@color/white"
                            android:contentDescription="@string/explore_tours_desc" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_welcome_message"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/hello_user"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/welcome_sub"
                                android:textSize="14sp"
                                android:textColor="@color/white"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_active_reservations"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/active_reservations_count"
                            android:textSize="13sp"
                            android:textColor="@color/white"
                            android:background="@drawable/bg_rounded_orange"
                            android:gravity="center"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="6dp"
                            android:minHeight="28dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Quick Actions -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:layout_marginTop="16dp">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_explore_tours"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:cardBackgroundColor="#FDFDFD"

                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp">

                            <FrameLayout
                                android:layout_width="48dp"
                                android:layout_height="48dp">



                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_explore"
                                    app:tint="@color/primary"
                                    android:contentDescription="@string/explore_tours_desc" />
                            </FrameLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/explore_tours"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:textAlignment="center"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_my_reservations"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:cardBackgroundColor="@color/white"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp">

                            <FrameLayout
                                android:layout_width="48dp"
                                android:layout_height="48dp">



                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_calendar_check"
                                    app:tint="@color/accent"
                                    android:contentDescription="@string/my_reservations_desc" />
                            </FrameLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/my_reservations_label"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:textAlignment="center"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_chats"
                        android:layout_width="0dp"
                        android:layout_height="100dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:cardBackgroundColor="@color/white"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp">

                            <FrameLayout
                                android:layout_width="48dp"
                                android:layout_height="48dp">

                                

                                <ImageView
                                    android:layout_width="32dp"
                                    android:layout_height="32dp"
                                    android:layout_gravity="center"
                                    android:src="@drawable/ic_chat"
                                    app:tint="@color/primary"
                                    android:contentDescription="@string/my_chats_desc" />
                            </FrameLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/my_chats_label"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:textAlignment="center"
                                android:layout_marginTop="8dp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Featured Tours Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tours Destacados"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginBottom="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_featured_tours"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:nestedScrollingEnabled="false"
                    android:orientation="horizontal" />
                <LinearLayout
                    android:id="@+id/empty_featured_tours"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:visibility="gone"
                    android:padding="24dp">

                    <ImageView
                        android:layout_width="72dp"
                        android:layout_height="72dp"
                        android:src="@drawable/ic_empty_box"
                        android:tint="#B0B0B0" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No hay Tours Destacados para mostrar"
                        android:textSize="14sp"
                        android:textColor="#9E9E9E"
                        android:layout_marginTop="12dp" />
                </LinearLayout>

                <!-- Popular Companies Section -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Empresas Populares"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginBottom="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_popular_companies"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingBottom="16dp"
                    android:nestedScrollingEnabled="false" />
                <LinearLayout
                    android:id="@+id/empty_popular_companies"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:visibility="gone"
                    android:padding="24dp">

                    <ImageView
                        android:layout_width="72dp"
                        android:layout_height="72dp"
                        android:src="@drawable/ic_empty_box"
                        android:tint="#B0B0B0" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No hay Empresas Populares para Mostrar"
                        android:textSize="14sp"
                        android:textColor="#9E9E9E"
                        android:layout_marginTop="12dp" />
                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="280dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:menu="@menu/client_nav_menu"
        app:headerLayout="@layout/nav_header_client"
        app:itemIconTint="?attr/colorOnSurfaceVariant"
        app:itemTextColor="?attr/colorOnSurface"
        app:itemShapeInsetStart="16dp"
        app:itemShapeInsetEnd="16dp"
        app:itemVerticalPadding="12dp"
        app:dividerInsetStart="16dp"
        app:dividerInsetEnd="16dp"
        app:itemShapeFillColor="@color/nav_active_indicator"/>

</androidx.drawerlayout.widget.DrawerLayout>