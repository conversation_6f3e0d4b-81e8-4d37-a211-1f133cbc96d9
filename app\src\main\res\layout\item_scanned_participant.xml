<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    android:background="?android:attr/selectableItemBackground">

    <TextView
        android:id="@+id/tv_participant_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Nombre del Participante"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/black" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="4dp">

        <TextView
            android:id="@+id/tv_checkin_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Check-in: Pendiente"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_checkin_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text=""
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_checkout_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="Check-out: Pendiente"
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray" />

    <TextView
        android:id="@+id/tv_checkout_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text=""
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:visibility="gone" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@android:color/darker_gray" />

</LinearLayout>
