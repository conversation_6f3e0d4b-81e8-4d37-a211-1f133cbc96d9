<?xml version="1.0" encoding="utf-8"?>
<!-- Copia temporal del bottom sheet de detalles para evitar XML vacío que rompe el build -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/bg_bottom_sheet_rounded"
    android:paddingTop="12dp"
    android:paddingBottom="0dp">

    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/bottom_sheet_handle"
        app:tint="#E0E0E0"
        android:layout_marginBottom="12dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingHorizontal="20dp"
        android:paddingBottom="12dp">

        <TextView
            android:id="@+id/tv_sheet_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Detalles del Tour"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#2C2C2C"
            tools:text="Tour Machu Picchu Express" />

        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            app:tint="#757575"
            android:contentDescription="Cerrar" />
    </LinearLayout>

</LinearLayout>
