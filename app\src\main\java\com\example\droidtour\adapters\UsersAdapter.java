package com.example.droidtour.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.example.droidtour.R;
import com.example.droidtour.models.User;
import java.util.ArrayList;
import java.util.List;

public class UsersAdapter extends RecyclerView.Adapter<UsersAdapter.UserViewHolder> implements Filterable {

    private List<User> userList;
    private List<User> userListFull;
    private OnUserClickListener listener;
    private String currentFilter = "ALL";
    private String currentSearchText = "";
    // Map para almacenar si un guía ha sido aprobado alguna vez (userId -> approved)
    private java.util.Map<String, Boolean> guideApprovalStatus = new java.util.HashMap<>();

    public interface OnUserClickListener {
        void onUserClick(User user);
        void onUserEdit(User user);
        void onUserDelete(User user);
        void onUserStatusChange(User user, boolean isActive);
    }

    public UsersAdapter(List<User> userList, OnUserClickListener listener) {
        this.userList = userList;
        this.userListFull = new ArrayList<>(userList);
        this.listener = listener;
    }

    @NonNull
    @Override
    public UserViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_user, parent, false);
        return new UserViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull UserViewHolder holder, int position) {
        User user = userList.get(position);
        holder.bind(user, listener);
    }

    @Override
    public int getItemCount() {
        return userList.size();
    }

    public void updateList(List<User> newList) {
        userList = new ArrayList<>(newList);
        userListFull = new ArrayList<>(newList);
        applyFilter(currentFilter); // Re-aplicar filtro actual
        notifyDataSetChanged();
    }
    
    /**
     * Establece el estado de aprobación de un guía
     * @param userId ID del usuario
     * @param approved true si ha sido aprobado alguna vez, false si nunca ha sido aprobado
     */
    public void setGuideApprovalStatus(String userId, boolean approved) {
        if (userId != null) {
            guideApprovalStatus.put(userId, approved);
        }
    }
    
    /**
     * Obtiene si un guía ha sido aprobado alguna vez
     * @param userId ID del usuario
     * @return true si ha sido aprobado, false si nunca ha sido aprobado o no se conoce
     */
    public boolean hasGuideBeenApproved(String userId) {
        if (userId == null) return false;
        Boolean approved = guideApprovalStatus.get(userId);
        return approved != null && approved;
    }

    public void setFilter(String filter) {
        this.currentFilter = filter;
        applyFilter(filter);
    }

    private void applyFilter(String filterType) {
        if (userListFull == null) return;

        // Primero aplicar filtro de texto si hay búsqueda activa
        List<User> textFilteredList;
        if (currentSearchText != null && !currentSearchText.isEmpty()) {
            // Aplicar filtro de texto primero
            textFilteredList = new ArrayList<>();
            String filterPattern = currentSearchText.toLowerCase().trim();
            
            for (User user : userListFull) {
                String fullName = user.getFullName() != null ? user.getFullName() : "";
                String firstName = user.getFirstName() != null ? user.getFirstName() : "";
                String lastName = user.getLastName() != null ? user.getLastName() : "";
                String combinedName = (firstName + " " + lastName).trim().toLowerCase();
                String email = user.getEmail() != null ? user.getEmail() : "";
                String type = user.getUserType() != null ? user.getUserType() : "";
                
                if (fullName.toLowerCase().contains(filterPattern) ||
                        combinedName.contains(filterPattern) ||
                        firstName.toLowerCase().contains(filterPattern) ||
                        lastName.toLowerCase().contains(filterPattern) ||
                        email.toLowerCase().contains(filterPattern) ||
                        type.toLowerCase().contains(filterPattern)) {
                    textFilteredList.add(user);
                }
            }
        } else {
            textFilteredList = new ArrayList<>(userListFull);
        }

        // Luego aplicar filtro de tipo sobre los resultados de búsqueda de texto
        List<User> filteredList = new ArrayList<>();

        switch (filterType) {
            case "ALL":
                filteredList.addAll(textFilteredList);
                break;
            case "ADMIN":
                for (User user : textFilteredList) {
                    String userType = user.getUserType();
                    if ("ADMIN".equals(userType) || "SUPERADMIN".equals(userType) || "COMPANY_ADMIN".equals(userType)) {
                        filteredList.add(user);
                    }
                }
                break;
            case "GUIDE":
                for (User user : textFilteredList) {
                    if ("GUIDE".equals(user.getUserType())) {
                        filteredList.add(user);
                    }
                }
                break;
            case "CLIENT":
                for (User user : textFilteredList) {
                    if ("CLIENT".equals(user.getUserType())) {
                        filteredList.add(user);
                    }
                }
                break;
        }

        userList = filteredList;
        notifyDataSetChanged();
    }

    @Override
    public Filter getFilter() {
        return userFilter;
    }

    private Filter userFilter = new Filter() {
        @Override
        protected FilterResults performFiltering(CharSequence constraint) {
            List<User> filteredList = new ArrayList<>();

            if (constraint == null || constraint.length() == 0) {
                filteredList.addAll(userListFull);
            } else {
                String filterPattern = constraint.toString().toLowerCase().trim();

                for (User user : userListFull) {
                    // Buscar en nombre completo
                    String fullName = user.getFullName() != null ? user.getFullName() : "";
                    
                    // Buscar en nombre y apellido por separado (por si fullName está vacío)
                    String firstName = user.getFirstName() != null ? user.getFirstName() : "";
                    String lastName = user.getLastName() != null ? user.getLastName() : "";
                    String combinedName = (firstName + " " + lastName).trim().toLowerCase();
                    
                    // Buscar en email
                    String email = user.getEmail() != null ? user.getEmail() : "";
                    
                    // Buscar en tipo de usuario
                    String type = user.getUserType() != null ? user.getUserType() : "";
                    
                    // Verificar si coincide con algún campo
                    if (fullName.toLowerCase().contains(filterPattern) ||
                            combinedName.contains(filterPattern) ||
                            firstName.toLowerCase().contains(filterPattern) ||
                            lastName.toLowerCase().contains(filterPattern) ||
                            email.toLowerCase().contains(filterPattern) ||
                            type.toLowerCase().contains(filterPattern)) {
                        filteredList.add(user);
                    }
                }
            }

            FilterResults results = new FilterResults();
            results.values = filteredList;
            return results;
        }

        @Override
        protected void publishResults(CharSequence constraint, FilterResults results) {
            // Guardar el texto de búsqueda actual
            currentSearchText = constraint != null ? constraint.toString() : "";
            
            @SuppressWarnings("unchecked")
            List<User> filteredByText = (List<User>) results.values;
            
            // Aplicar filtro de tipo sobre los resultados de búsqueda de texto
            if (filteredByText != null) {
                List<User> finalFilteredList = new ArrayList<>();
                
                switch (currentFilter) {
                    case "ALL":
                        finalFilteredList.addAll(filteredByText);
                        break;
                    case "ADMIN":
                        for (User user : filteredByText) {
                            String userType = user.getUserType();
                            if ("ADMIN".equals(userType) || "SUPERADMIN".equals(userType) || "COMPANY_ADMIN".equals(userType)) {
                                finalFilteredList.add(user);
                            }
                        }
                        break;
                    case "GUIDE":
                        for (User user : filteredByText) {
                            if ("GUIDE".equals(user.getUserType())) {
                                finalFilteredList.add(user);
                            }
                        }
                        break;
                    case "CLIENT":
                        for (User user : filteredByText) {
                            if ("CLIENT".equals(user.getUserType())) {
                                finalFilteredList.add(user);
                            }
                        }
                        break;
                }
                
                userList.clear();
                userList.addAll(finalFilteredList);
                notifyDataSetChanged();
            }
        }
    };

    class UserViewHolder extends RecyclerView.ViewHolder {
        private TextView tvUserName, tvUserEmail, tvUserType, tvAvatarInitial;
        private ImageView ivUserAvatar;
        private View viewStatusIndicator;
        private com.google.android.material.switchmaterial.SwitchMaterial switchUserStatus;
        private com.google.android.material.chip.Chip chipStatus;
        private com.google.android.material.button.MaterialButton btnEditUser;

        public UserViewHolder(@NonNull View itemView) {
            super(itemView);
            tvUserName = itemView.findViewById(R.id.tv_user_name);
            tvUserEmail = itemView.findViewById(R.id.tv_user_email);
            tvUserType = itemView.findViewById(R.id.tv_user_type);
            tvAvatarInitial = itemView.findViewById(R.id.tv_avatar_initial);
            ivUserAvatar = itemView.findViewById(R.id.iv_user_avatar);
            viewStatusIndicator = itemView.findViewById(R.id.view_status_indicator);
            switchUserStatus = itemView.findViewById(R.id.switch_user_status);
            chipStatus = itemView.findViewById(R.id.chip_status);
            btnEditUser = itemView.findViewById(R.id.btn_edit_user);
        }

        public void bind(User user, OnUserClickListener listener) {
            // Nombre y email seguros
            tvUserName.setText(user.getFullName() != null ? user.getFullName() : "Sin nombre");
            tvUserEmail.setText(user.getEmail() != null ? user.getEmail() : "Sin email");
            tvUserType.setText(getUserTypeDisplayName(user.getUserType()));

            // Avatar e inicial
            setupUserAvatar(user);

            // Estado en línea (placeholder)
            setupOnlineStatus(user);

            // Configurar chip "Pendiente" y botón "Editar" para guías
            setupGuidePendingStatus(user);

            // Switch de estado
            setupStatusSwitch(user, listener);

            // Listeners de clic
            setupClickListeners(user, listener);
        }
        
        /**
         * Configura el chip "Pendiente" y el botón "Editar" para guías
         * El chip "Pendiente" solo aparece si:
         * - Es GUIDE
         * - status = "pending"
         * - Nunca ha sido aprobado (approved = false en user_roles)
         */
        private void setupGuidePendingStatus(User user) {
            if ("GUIDE".equals(user.getUserType()) && 
                "pending".equalsIgnoreCase(user.getStatus()) &&
                !UsersAdapter.this.hasGuideBeenApproved(user.getUserId())) {
                // Mostrar chip "Pendiente" y ocultar botón "Editar"
                chipStatus.setVisibility(View.VISIBLE);
                chipStatus.setText("Pendiente");
                chipStatus.setChipBackgroundColorResource(R.color.notification_orange);
                chipStatus.setTextColor(itemView.getContext().getResources().getColor(R.color.white));
                btnEditUser.setVisibility(View.GONE);
            } else {
                // Ocultar chip "Pendiente" y mostrar botón "Editar"
                chipStatus.setVisibility(View.GONE);
                btnEditUser.setVisibility(View.VISIBLE);
            }
        }

        private void setupUserAvatar(User user) {
            String name = user.getFullName() != null ? user.getFullName() : "";
            String initial = name.isEmpty() ? "?" : name.substring(0, 1).toUpperCase();
            tvAvatarInitial.setText(initial);

            // Cargar foto si existe - verificar tanto getPhotoUrl() como directamente personalData
            String photo = null;
            if (user.getPersonalData() != null) {
                photo = user.getPersonalData().getProfileImageUrl();
            }
            // Fallback al método legacy si no se encuentra en personalData
            if ((photo == null || photo.isEmpty()) && user.getPhotoUrl() != null) {
                photo = user.getPhotoUrl();
            }
            
            if (photo != null && !photo.isEmpty()) {
                android.util.Log.d("UsersAdapter", "Cargando avatar desde URL: " + photo);
                Glide.with(itemView.getContext())
                        .load(photo)
                        .placeholder(R.drawable.ic_avatar_24)
                        .error(R.drawable.ic_avatar_24)
                        .circleCrop()
                        .into(ivUserAvatar);
                tvAvatarInitial.setVisibility(View.GONE);
            } else {
                android.util.Log.d("UsersAdapter", "No hay URL de imagen para usuario: " + user.getEmail());
                tvAvatarInitial.setVisibility(View.VISIBLE);
                ivUserAvatar.setImageDrawable(null);
            }
        }

        private void setupOnlineStatus(User user) {
            // Placeholder: ocultamos el indicador
            viewStatusIndicator.setVisibility(View.GONE);
        }


        private void setupStatusSwitch(User user, OnUserClickListener listener) {
            // Determinar si está activo basándose en el status // nuevo actualizado
            boolean isActive = "active".equalsIgnoreCase(user.getStatus());

            // Primero remover el listener para evitar que se dispare durante el setChecked
            switchUserStatus.setOnCheckedChangeListener(null);
            // Luego establecer el estado del switch
            switchUserStatus.setChecked(isActive);
            // Finalmente asignar el nuevo listener
            switchUserStatus.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (listener != null) {
                    listener.onUserStatusChange(user, isChecked);
                }
            });
        }

        private void setupClickListeners(User user, OnUserClickListener listener) {
            itemView.findViewById(R.id.btn_view_user).setOnClickListener(v -> {
                if (listener != null) listener.onUserClick(user);
            });

            itemView.findViewById(R.id.btn_edit_user).setOnClickListener(v -> {
                if (listener != null) listener.onUserEdit(user);
            });

            itemView.findViewById(R.id.btn_delete_user).setOnClickListener(v -> {
                if (listener != null) listener.onUserDelete(user);
            });

            itemView.setOnClickListener(v -> {
                if (listener != null) listener.onUserClick(user);
            });
        }

        private String getUserTypeDisplayName(String userType) {
            if (userType == null) return "Sin tipo";

            switch (userType) {
                case "SUPERADMIN": return "Super Admin";
                case "ADMIN": return "Administrador";
                case "COMPANY_ADMIN": return "Administrador de empresa";
                case "GUIDE": return "Guía Turístico";
                case "CLIENT": return "Cliente";
                default: return userType;
            }
        }

        private int getChipColorForUserType(String userType) {
            if (userType == null) return R.color.default_chip_color;

            switch (userType) {
                case "SUPERADMIN": return R.color.superadmin_chip_color;
                case "ADMIN": return R.color.admin_chip_color;
                case "COMPANY_ADMIN": return R.color.admin_chip_color;
                case "GUIDE": return R.color.guide_chip_color;
                case "CLIENT": return R.color.client_chip_color;
                default: return R.color.default_chip_color;
            }
        }


    }
}