<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Guide Photo -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_guide_photo"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@android:drawable/ic_menu_myplaces"
            android:scaleType="centerCrop"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            android:background="@color/light_gray" />

        <!-- Guide Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_guide_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="María González"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_guide_languages"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Español, Inglés, Francés"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginTop="4dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@android:drawable/btn_star_big_on"
                    android:tint="@color/orange" />

                <TextView
                    android:id="@+id/tv_guide_rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4.8"
                    android:textSize="14sp"
                    android:textColor="@color/black"
                    android:layout_marginStart="4dp" />

                <TextView
                    android:id="@+id/tv_guide_tours"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(25 tours)"
                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_send_proposal"
                style="@style/Widget.Material3.Button"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Enviar Propuesta"
                android:textSize="12sp"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_profile"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Ver Perfil"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
