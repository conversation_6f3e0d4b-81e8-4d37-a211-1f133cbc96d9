<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Ver Detalles"
            app:titleTextColor="@color/white"
            app:navigationIcon="@android:drawable/ic_menu_revert" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Scrollable Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="80dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Tour Header Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Tour Image Placeholder -->
                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:src="@android:drawable/ic_menu_gallery"
                        android:scaleType="centerCrop"
                        android:background="@color/light_gray"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="City Tour Lima Centro Histórico"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_info_details"
                            android:tint="@color/gray" />

                        <TextView
                            android:id="@+id/tv_company_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Inka Travel Peru"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_marginStart="8dp" />

                    </LinearLayout>

                    <!-- Tour Basic Info Grid -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📅 Fecha"
                                android:textSize="12sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_tour_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Hoy"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⏰ Hora"
                                android:textSize="12sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_tour_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="09:30 AM"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⏱️ Duración"
                                android:textSize="12sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_tour_duration"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3 horas"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Payment and Participants -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="💰 Pago Ofrecido"
                                android:textSize="14sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_payment_amount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="S/. 200.00"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="@color/green"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="end">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="👥 Participantes"
                                android:textSize="14sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_participants"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="6 personas"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Tour Description Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📝 Descripción del Tour"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_tour_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Descripción detallada del tour..."
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Meeting Point Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📍 Punto de Encuentro"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_meeting_point"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Plaza Mayor de Lima..."
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- What's Included Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="✅ Incluye"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_included"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Guía turístico profesional..."
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- What's Not Included Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="❌ No Incluye"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_not_included"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Almuerzo..."
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Requirements Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋 Requisitos"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_requirements"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Documento de identidad..."
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>



        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="bottom"
        android:background="@color/white"
        android:padding="16dp"
        android:elevation="8dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_reject_offer"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Rechazar"
            android:textColor="@color/red"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_contact_company"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Contactar"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_accept_offer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Aceptar"
            android:backgroundTint="@color/primary" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
