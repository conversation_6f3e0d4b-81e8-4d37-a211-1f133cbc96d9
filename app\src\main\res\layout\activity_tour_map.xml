<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Ubicación del Tour"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert" />

    <!-- Scrollable Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Tour Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🗺️ Tour"
                        android:textSize="16sp"
                        android:textColor="@color/gray"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="City Tour Lima Centro Histórico"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Map Placeholder Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Map Placeholder -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="250dp"
                        android:background="@color/light_gray"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:src="@android:drawable/ic_menu_mapmode"
                            android:tint="@color/gray"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mapa del Tour"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/gray"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Integración con Google Maps API"
                            android:textSize="14sp"
                            android:textColor="@color/gray" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Location Details Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📍 Ubicación"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Address -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@android:drawable/ic_menu_mylocation"
                            android:tint="@color/gray"
                            android:layout_marginEnd="12dp"
                            android:layout_marginTop="2dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Dirección"
                                android:textSize="12sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_location_address"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Plaza de Armas, Lima"
                                android:textSize="16sp"
                                android:textColor="@color/black"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Coordinates -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@android:drawable/ic_menu_compass"
                            android:tint="@color/gray"
                            android:layout_marginEnd="12dp"
                            android:layout_marginTop="2dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Coordenadas"
                                android:textSize="12sp"
                                android:textColor="@color/gray" />

                            <TextView
                                android:id="@+id/tv_location_coordinates"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Lat: -12.0464, Lng: -77.0428"
                                android:textSize="14sp"
                                android:textColor="@color/black"
                                android:fontFamily="monospace"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>



        </LinearLayout>

    </ScrollView>

</LinearLayout>
