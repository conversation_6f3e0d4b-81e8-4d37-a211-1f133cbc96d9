<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Company Logo -->
        <ImageView
            android:id="@+id/iv_company_logo"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@android:drawable/ic_menu_info_details"
            android:background="@color/light_gray"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Company Info -->
        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Tours Cusco Adventures"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginStart="12dp"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintEnd_toStartOf="@id/layout_rating"
            app:layout_constraintTop_toTopOf="@id/iv_company_logo" />

        <TextView
            android:id="@+id/tv_company_location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Cusco, Perú"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintEnd_toStartOf="@id/layout_rating"
            app:layout_constraintTop_toBottomOf="@id/tv_company_name" />

        <TextView
            android:id="@+id/tv_tours_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="12 tours disponibles"
            android:textSize="12sp"
            android:textColor="@color/primary"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintEnd_toStartOf="@id/layout_rating"
            app:layout_constraintTop_toBottomOf="@id/tv_company_location" />

        <!-- Rating -->
        <LinearLayout
            android:id="@+id/layout_rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4.5"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/orange" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@android:drawable/btn_star_big_on"
                    android:tint="@color/orange"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_reviews_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="(45 reseñas)"
                android:textSize="10sp"
                android:textColor="@color/gray"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- Services Tags -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_services"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@id/iv_company_logo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.chip.Chip
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Guía Profesional"
                android:textSize="10sp"
                app:chipBackgroundColor="@color/light_gray"
                app:chipStrokeWidth="0dp" />

            <com.google.android.material.chip.Chip
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Transporte"
                android:textSize="10sp"
                app:chipBackgroundColor="@color/light_gray"
                app:chipStrokeWidth="0dp" />

            <com.google.android.material.chip.Chip
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Almuerzo"
                android:textSize="10sp"
                app:chipBackgroundColor="@color/light_gray"
                app:chipStrokeWidth="0dp" />

        </com.google.android.material.chip.ChipGroup>

        <!-- Price Range -->
        <TextView
            android:id="@+id/tv_price_range"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Desde S/. 120"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/green"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chip_group_services" />

        <!-- View Tours Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_view_tours"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ver Tours"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chip_group_services" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
