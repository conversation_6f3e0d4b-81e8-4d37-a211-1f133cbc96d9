<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Chat de Atención al Cliente"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Active Chats List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_chat_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/card_chat_stats" />

    <!-- Chat Statistics Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_chat_stats"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_active_chats"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/green" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Activos"
                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_pending_chats"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/orange" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Pendientes"
                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_avg_response_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2.5 min"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="T. Respuesta"
                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_chats"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/rv_chat_list"
        app:layout_constraintBottom_toBottomOf="@id/rv_chat_list"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@android:drawable/ic_menu_help"
            android:tint="@color/gray" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay conversaciones activas"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Los clientes podrán contactarte aquí"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
