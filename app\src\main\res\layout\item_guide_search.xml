<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardElevation="3dp"
    app:cardCornerRadius="16dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/white">

        <!-- Header con gradiente sutil -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical"
            android:background="@color/primary_light">

        <!-- Foto del guía con badge de rating -->
        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/img_guide"
                android:layout_width="71dp"
                android:layout_height="71dp"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_person"
                app:strokeWidth="2dp"
                app:strokeColor="@color/primary_light"
                android:layout_margin="2dp"
                app:shapeAppearanceOverlay="@style/CircleImageView" />

            <!-- Badge de rating -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                app:cardElevation="3dp"
                app:cardCornerRadius="12dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_star"
                        android:tint="@color/warning" />

                    <TextView
                        android:id="@+id/tv_rating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="5.0"
                        android:textSize="11sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="2dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </FrameLayout>

            <!-- Información del guía -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="16dp">

                <TextView
                    android:id="@+id/tv_guide_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Nombre del Guía"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <!-- Idiomas con icono -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="8dp">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_language"
                        android:tint="@color/primary" />

                    <TextView
                        android:id="@+id/tv_languages"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Español, English"
                        android:textSize="13sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginStart="6dp"
                        android:maxLines="2"
                        android:ellipsize="end" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider"
            android:alpha="0.5" />

        <!-- Botón hacer propuesta en sección separada -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_send_proposal"
                android:layout_width="0dp"
                android:layout_height="48dp"
                app:backgroundTint="@color/primary"
                android:layout_weight="1"
                android:text="Hacer Propuesta"
                android:textSize="14sp"
                android:textStyle="bold"
                app:icon="@drawable/ic_send"
                android:textColor="@color/white"
                app:iconTint="@color/white"
                app:iconSize="20dp"
                app:iconGravity="start"
                style="@style/Widget.Material3.Button.ElevatedButton" />

            <ImageButton
                android:id="@+id/btn_show_guide_details"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/ic_info"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:tint="@color/primary"
                android:contentDescription="Ver detalles del guía" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
