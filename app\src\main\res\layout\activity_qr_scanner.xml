<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Escanear QR"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleCentered="true"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back_24"
        app:navigationIconTint="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

    <!-- Camera Preview Placeholder (styled as card container) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_camera_preview"
        android:layout_width="0dp"
        android:layout_height="280dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="14dp"
        app:cardCornerRadius="18dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/black"
        app:strokeWidth="1dp"
        app:strokeColor="#1AFFFFFF"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Subtle overlay -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#22000000" />

            <!-- Center content (placeholder) -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="18dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Apunta al código QR"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mantén el QR dentro del marco"
                    android:textSize="13sp"
                    android:textColor="#DFFFFFFF"
                    android:layout_marginBottom="18dp" />

                <!-- QR Frame (more modern) -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="230dp"
                    android:layout_height="230dp"
                    app:cardCornerRadius="18dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#00FFFFFF"
                    app:strokeWidth="2dp"
                    app:strokeColor="#66FFFFFF">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="#12000000" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:text="Coloca el QR aquí"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="#EFFFFFFF" />

                    </FrameLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </FrameLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Scan Actions (as a clean action bar) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_scan_actions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="14dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/white"
        app:strokeWidth="1dp"
        app:strokeColor="#ECECEC"
        app:layout_constraintTop_toBottomOf="@id/layout_camera_preview"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="12dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_flash"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Flash"
                android:textAllCaps="false"
                app:cornerRadius="12dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:icon="@drawable/ic_flash"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_finish_checkin"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Finalizar Check-In"
                android:textAllCaps="false"
                app:cornerRadius="12dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:textColor="@color/primary"
                android:visibility="gone" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Scan Information Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_scan_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="12dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/white"
        app:strokeWidth="1dp"
        app:strokeColor="#ECECEC"
        app:layout_constraintTop_toBottomOf="@id/layout_scan_actions"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/card_scan_history">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <FrameLayout
                    android:layout_width="38dp"
                    android:layout_height="38dp"
                    android:layout_marginEnd="10dp">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="38dp"
                        android:layout_height="38dp"
                        app:cardCornerRadius="19dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#1A2196F3" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_info"
                        android:tint="@color/primary" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Información del Tour"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#202124" />

            </LinearLayout>

            <!-- rows -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="92dp"
                    android:layout_height="wrap_content"
                    android:text="Tour"
                    android:textSize="13sp"
                    android:textColor="#6B7280"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="City Tour Lima Centro"
                    android:textSize="13sp"
                    android:textColor="#202124"
                    android:maxLines="2"
                    android:ellipsize="end" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="92dp"
                    android:layout_height="wrap_content"
                    android:text="Participantes"
                    android:textSize="13sp"
                    android:textColor="#6B7280"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_participants_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="6 personas"
                    android:textSize="13sp"
                    android:textColor="#202124" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="92dp"
                    android:layout_height="wrap_content"
                    android:text="Estado"
                    android:textSize="13sp"
                    android:textColor="#6B7280"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_scan_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Esperando QR de Check-in"
                    android:textSize="13sp"
                    android:textColor="@color/orange"
                    android:maxLines="2"
                    android:ellipsize="end" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="92dp"
                    android:layout_height="wrap_content"
                    android:text="Progreso"
                    android:textSize="13sp"
                    android:textColor="#6B7280"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_checkinout_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="0 / 0 participantes"
                    android:textSize="13sp"
                    android:textColor="@color/primary"
                    android:maxLines="2"
                    android:ellipsize="end" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Historial de Escaneos Card (mismo formato que Información del Tour) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_scan_history"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/white"
        app:strokeWidth="1dp"
        app:strokeColor="#ECECEC"
        app:layout_constraintTop_toBottomOf="@id/card_scan_info"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <FrameLayout
                    android:layout_width="38dp"
                    android:layout_height="38dp"
                    android:layout_marginEnd="10dp">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="38dp"
                        android:layout_height="38dp"
                        app:cardCornerRadius="19dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#1A2196F3" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@android:drawable/ic_menu_recent_history"
                        android:tint="@color/primary" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Historial de escaneos"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#202124" />

            </LinearLayout>

            <!-- Mensaje cuando no hay escaneos -->
            <TextView
                android:id="@+id/tv_empty_history"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Aún no se han escaneado participantes"
                android:textSize="13sp"
                android:textColor="#999999"
                android:gravity="center"
                android:padding="16dp"
                android:visibility="visible" />

            <!-- Lista de participantes escaneados -->
            <LinearLayout
                android:id="@+id/layout_scanned_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</LinearLayout>

<!-- Success/Error Overlay -->
<com.google.android.material.card.MaterialCardView
    android:id="@+id/card_scan_result"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="24dp"
    android:layout_gravity="center"
    app:cardCornerRadius="18dp"
    app:cardElevation="10dp"
    android:visibility="gone"
    app:cardBackgroundColor="@color/white"
    app:strokeWidth="1dp"
    app:strokeColor="#ECECEC">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="22dp"
            android:gravity="center">

            <FrameLayout
                android:layout_width="76dp"
                android:layout_height="76dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="76dp"
                    android:layout_height="76dp"
                    app:cardCornerRadius="38dp"
                    app:cardElevation="0dp"
                    app:cardBackgroundColor="#1A4CAF50" />

                <ImageView
                    android:id="@+id/iv_scan_result_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:src="@android:drawable/ic_menu_agenda"
                    android:tint="@color/green" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_scan_result_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¡Check-in Exitoso!"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/green"
                android:layout_marginTop="16dp" />

            <TextView
                android:id="@+id/tv_scan_result_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Cliente registrado correctamente"
                android:textSize="15sp"
                android:textColor="#202124"
                android:gravity="center"
                android:layout_marginTop="8dp" />

            <TextView
                android:id="@+id/tv_client_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ana García Pérez"
                android:textSize="17sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginTop="12dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_continue_scanning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Continuar"
                android:textAllCaps="false"
                android:layout_marginTop="16dp"
                app:cornerRadius="12dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</FrameLayout>
