<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- RecyclerView para guías activos -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_active_guides"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp"
        android:clipToPadding="false" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_people_24"
            android:alpha="0.3"
            app:tint="#9E9E9E" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay guías activos"
            android:textColor="#424242"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Los guías que acepten tus propuestas aparecerán aquí"
            android:textColor="#757575"
            android:textSize="14sp"
            android:layout_marginTop="8dp"
            android:gravity="center" />
    </LinearLayout>
</LinearLayout>
