package com.example.droidtour.superadmin.helpers;

import android.content.ContentValues;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.pdf.PdfDocument;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import com.example.droidtour.R;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.charts.PieChart;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Helper class para exportación completa de gráficos (PDF + imágenes)
 * Soporta Android 10+ (MediaStore) y versiones anteriores
 */
public class DashboardExportHelper {
    
    private static final String TAG = "DashboardExportHelper";
    private final Context context;
    
    public DashboardExportHelper(Context context) {
        this.context = context;
    }
    
    /**
     * Exporta reporte completo: PDF con KPIs + imágenes de gráficos
     */
    public void exportAnalyticsReport(List<ChartInfo> charts, String[] kpiLabels, String[] kpiValues, 
                                     ExportCompleteCallback callback) {
        try {
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ usar MediaStore
                exportWithMediaStore(charts, kpiLabels, kpiValues, timestamp, callback);
            } else {
                // Android 9 y anteriores usar directorio tradicional
                File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
                File droidTourDir = new File(downloadsDir, "DroidTour");
                if (!droidTourDir.exists() && !droidTourDir.mkdirs()) {
                    if (callback != null) {
                        callback.onError(new IOException("No se pudo crear el directorio de descarga"));
                    }
                    return;
                }
                exportWithFileSystem(charts, kpiLabels, kpiValues, timestamp, droidTourDir, callback);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error en exportación", e);
            if (callback != null) {
                callback.onError(e);
            }
        }
    }
    
    /**
     * Exporta usando MediaStore (Android 10+)
     */
    private void exportWithMediaStore(List<ChartInfo> charts, String[] kpiLabels, String[] kpiValues,
                                     String timestamp, ExportCompleteCallback callback) {
        try {
            // Exportar solo PDF con gráficos incluidos
            String pdfPath = exportPDFWithMediaStore(charts, kpiLabels, kpiValues, timestamp);
            
            // Verificar que el PDF se guardó correctamente (debe ser un URI válido)
            if (pdfPath == null || !pdfPath.startsWith("content://")) {
                throw new IOException("El PDF no se guardó correctamente. Path recibido: " + pdfPath);
            }
            
            // No exportar imágenes, solo PDF
            if (callback != null) {
                callback.onSuccess(pdfPath, new ArrayList<>());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error exportando con MediaStore", e);
            if (callback != null) {
                callback.onError(e);
            }
        }
    }
    
    /**
     * Exporta usando sistema de archivos tradicional (Android 9-)
     */
    private void exportWithFileSystem(List<ChartInfo> charts, String[] kpiLabels, String[] kpiValues,
                                      String timestamp, File directory, ExportCompleteCallback callback) {
        try {
            // Exportar solo PDF con gráficos incluidos
            String pdfPath = exportPDFWithFileSystem(charts, kpiLabels, kpiValues, timestamp, directory);
            
            // Verificar que el PDF se guardó correctamente
            if (pdfPath == null || !new File(pdfPath).exists()) {
                throw new IOException("El PDF no se guardó correctamente");
            }
            
            // No exportar imágenes, solo PDF
            if (callback != null) {
                callback.onSuccess(pdfPath, new ArrayList<>());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error exportando con sistema de archivos", e);
            if (callback != null) {
                callback.onError(e);
            }
        }
    }
    
    /**
     * Exporta PDF con KPIs y gráficos usando MediaStore
     */
    private String exportPDFWithMediaStore(List<ChartInfo> charts, String[] kpiLabels, String[] kpiValues, String timestamp) throws IOException {
        PdfDocument document = new PdfDocument();
        PdfDocument.PageInfo pageInfo = new PdfDocument.PageInfo.Builder(595, 842, 1).create();
        PdfDocument.Page page = document.startPage(pageInfo);
        
        Canvas canvas = page.getCanvas();
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        
        int margin = 50;
        int yPos = 80;
        int lineHeight = 20;
        int bottomMargin = 120;
        int maxYPos = 842 - bottomMargin;
        int pageNumber = 1;
        
        // Título
        paint.setTextSize(24);
        paint.setColor(context.getResources().getColor(R.color.primary));
        paint.setFakeBoldText(true);
        canvas.drawText("Reporte Analytics - DroidTour", margin, yPos, paint);
        yPos += 30;
        
        // Fecha
        paint.setTextSize(14);
        paint.setColor(Color.BLACK);
        paint.setFakeBoldText(false);
        String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(new Date());
        canvas.drawText("Generado: " + currentDate, margin, yPos, paint);
        yPos += 30;
        
        // KPIs
        paint.setTextSize(16);
        paint.setFakeBoldText(true);
        paint.setColor(Color.BLACK);
        canvas.drawText("Indicadores Clave de Rendimiento", margin, yPos, paint);
        yPos += lineHeight + 5;
        
        paint.setTextSize(12);
        paint.setFakeBoldText(false);
        if (kpiLabels != null && kpiValues != null) {
            for (int i = 0; i < Math.min(kpiLabels.length, kpiValues.length); i++) {
                canvas.drawText("• " + kpiLabels[i] + ": " + kpiValues[i], margin + 20, yPos, paint);
                yPos += lineHeight;
            }
        }
        
        yPos += 20;
        
        // Gráficos
        if (charts != null && !charts.isEmpty()) {
            paint.setTextSize(16);
            paint.setFakeBoldText(true);
            paint.setColor(Color.BLACK);
            canvas.drawText("Gráficos de Análisis", margin, yPos, paint);
            yPos += lineHeight + 10;
            
            int chartWidth = 495; // Ancho disponible en el PDF
            int chartHeight = 200; // Altura inicial para cada gráfico
            
            for (ChartInfo chartInfo : charts) {
                if (chartInfo.chart != null && chartInfo.chart instanceof View) {
                    // Verificar si necesitamos nueva página
                    if (yPos + chartHeight + 50 > maxYPos) {
                        // Pie de página antes de finalizar
                        paint.setTextSize(10);
                        paint.setColor(Color.GRAY);
                        canvas.drawText("DroidTour SuperAdmin Dashboard - Confidencial", margin, 820, paint);
                        document.finishPage(page);
                        pageNumber++;
                        pageInfo = new PdfDocument.PageInfo.Builder(595, 842, pageNumber).create();
                        page = document.startPage(pageInfo);
                        canvas = page.getCanvas();
                        yPos = 80;
                    }
                    
                    // Capturar gráfico como bitmap
                    Bitmap chartBitmap = captureChartAsBitmap((View) chartInfo.chart, chartWidth, chartHeight);
                    if (chartBitmap != null) {
                        // Título del gráfico
                        paint.setTextSize(12);
                        paint.setFakeBoldText(true);
                        paint.setColor(Color.BLACK);
                        canvas.drawText(chartInfo.title, margin, yPos, paint);
                        yPos += lineHeight + 5;
                        
                        // Ajustar tamaño del gráfico para que quepa en el PDF
                        int scaledHeight = Math.min(chartHeight, maxYPos - yPos - 30);
                        int scaledWidth = (int) (chartBitmap.getWidth() * (scaledHeight / (float) chartBitmap.getHeight()));
                        if (scaledWidth > chartWidth) {
                            scaledWidth = chartWidth;
                            scaledHeight = (int) (chartBitmap.getHeight() * (chartWidth / (float) chartBitmap.getWidth()));
                        }
                        
                        // Centrar gráfico
                        int chartX = margin + (chartWidth - scaledWidth) / 2;
                        canvas.drawBitmap(Bitmap.createScaledBitmap(chartBitmap, scaledWidth, scaledHeight, true),
                                        chartX, yPos, null);
                        yPos += scaledHeight + 20;
                        
                        chartBitmap.recycle();
                    }
                }
            }
        }
        
        // Pie de página antes de finalizar la última página
        paint.setTextSize(10);
        paint.setColor(Color.GRAY);
        canvas.drawText("DroidTour SuperAdmin Dashboard - Confidencial", margin, 820, paint);
        
        document.finishPage(page);
        
        // Guardar usando MediaStore
        String fileName = "Reporte_Analytics_" + timestamp + ".pdf";
        ContentValues contentValues = new ContentValues();
        contentValues.put(MediaStore.Downloads.DISPLAY_NAME, fileName);
        contentValues.put(MediaStore.Downloads.MIME_TYPE, "application/pdf");
        contentValues.put(MediaStore.Downloads.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS + "/DroidTour");
        
        Uri uri = context.getContentResolver().insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues);
        if (uri != null) {
            FileOutputStream fos = (FileOutputStream) context.getContentResolver().openOutputStream(uri);
            if (fos != null) {
                document.writeTo(fos);
                fos.close();
                document.close();
                return uri.toString();
            }
        }
        
        document.close();
        throw new IOException("No se pudo guardar el PDF usando MediaStore");
    }
    
    /**
     * Exporta PDF con KPIs y gráficos usando sistema de archivos
     */
    private String exportPDFWithFileSystem(List<ChartInfo> charts, String[] kpiLabels, String[] kpiValues, String timestamp, File directory) throws IOException {
        PdfDocument document = new PdfDocument();
        PdfDocument.PageInfo pageInfo = new PdfDocument.PageInfo.Builder(595, 842, 1).create();
        PdfDocument.Page page = document.startPage(pageInfo);
        
        Canvas canvas = page.getCanvas();
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        
        int margin = 50;
        int yPos = 80;
        int lineHeight = 20;
        int bottomMargin = 120;
        int maxYPos = 842 - bottomMargin;
        int pageNumber = 1;
        
        // Título
        paint.setTextSize(24);
        paint.setColor(context.getResources().getColor(R.color.primary));
        paint.setFakeBoldText(true);
        canvas.drawText("Reporte Analytics - DroidTour", margin, yPos, paint);
        yPos += 30;
        
        // Fecha
        paint.setTextSize(14);
        paint.setColor(Color.BLACK);
        paint.setFakeBoldText(false);
        String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(new Date());
        canvas.drawText("Generado: " + currentDate, margin, yPos, paint);
        yPos += 30;
        
        // KPIs
        paint.setTextSize(16);
        paint.setFakeBoldText(true);
        paint.setColor(Color.BLACK);
        canvas.drawText("Indicadores Clave de Rendimiento", margin, yPos, paint);
        yPos += lineHeight + 5;
        
        paint.setTextSize(12);
        paint.setFakeBoldText(false);
        if (kpiLabels != null && kpiValues != null) {
            for (int i = 0; i < Math.min(kpiLabels.length, kpiValues.length); i++) {
                canvas.drawText("• " + kpiLabels[i] + ": " + kpiValues[i], margin + 20, yPos, paint);
                yPos += lineHeight;
            }
        }
        
        yPos += 20;
        
        // Gráficos
        if (charts != null && !charts.isEmpty()) {
            paint.setTextSize(16);
            paint.setFakeBoldText(true);
            paint.setColor(Color.BLACK);
            canvas.drawText("Gráficos de Análisis", margin, yPos, paint);
            yPos += lineHeight + 10;
            
            int chartWidth = 495; // Ancho disponible en el PDF
            int chartHeight = 200; // Altura inicial para cada gráfico
            
            for (ChartInfo chartInfo : charts) {
                if (chartInfo.chart != null && chartInfo.chart instanceof View) {
                    // Verificar si necesitamos nueva página
                    if (yPos + chartHeight + 50 > maxYPos) {
                        // Pie de página antes de finalizar
                        paint.setTextSize(10);
                        paint.setColor(Color.GRAY);
                        canvas.drawText("DroidTour SuperAdmin Dashboard - Confidencial", margin, 820, paint);
                        document.finishPage(page);
                        pageNumber++;
                        pageInfo = new PdfDocument.PageInfo.Builder(595, 842, pageNumber).create();
                        page = document.startPage(pageInfo);
                        canvas = page.getCanvas();
                        yPos = 80;
                    }
                    
                    // Capturar gráfico como bitmap
                    Bitmap chartBitmap = captureChartAsBitmap((View) chartInfo.chart, chartWidth, chartHeight);
                    if (chartBitmap != null) {
                        // Título del gráfico
                        paint.setTextSize(12);
                        paint.setFakeBoldText(true);
                        paint.setColor(Color.BLACK);
                        canvas.drawText(chartInfo.title, margin, yPos, paint);
                        yPos += lineHeight + 5;
                        
                        // Ajustar tamaño del gráfico para que quepa en el PDF
                        int scaledHeight = Math.min(chartHeight, maxYPos - yPos - 30);
                        int scaledWidth = (int) (chartBitmap.getWidth() * (scaledHeight / (float) chartBitmap.getHeight()));
                        if (scaledWidth > chartWidth) {
                            scaledWidth = chartWidth;
                            scaledHeight = (int) (chartBitmap.getHeight() * (chartWidth / (float) chartBitmap.getWidth()));
                        }
                        
                        // Centrar gráfico
                        int chartX = margin + (chartWidth - scaledWidth) / 2;
                        canvas.drawBitmap(Bitmap.createScaledBitmap(chartBitmap, scaledWidth, scaledHeight, true),
                                        chartX, yPos, null);
                        yPos += scaledHeight + 20;
                        
                        chartBitmap.recycle();
                    }
                }
            }
        }
        
        // Pie de página antes de finalizar la última página
        paint.setTextSize(10);
        paint.setColor(Color.GRAY);
        canvas.drawText("DroidTour SuperAdmin Dashboard - Confidencial", margin, 820, paint);
        
        document.finishPage(page);
        
        // Guardar archivo
        String fileName = "Reporte_Analytics_" + timestamp + ".pdf";
        File file = new File(directory, fileName);
        FileOutputStream fos = new FileOutputStream(file);
        document.writeTo(fos);
        document.close();
        fos.close();
        
        return file.getAbsolutePath();
    }
    
    /**
     * Exporta imágenes de gráficos usando MediaStore
     */
    private List<String> exportImagesWithMediaStore(List<ChartInfo> charts, String timestamp) throws IOException {
        List<String> imagePaths = new ArrayList<>();
        int chartWidth = 1200;
        int chartHeight = 800;
        String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(new Date());
        
        for (ChartInfo chartInfo : charts) {
            if (chartInfo.chart != null && chartInfo.chart instanceof View) {
                Bitmap chartBitmap = captureChartAsBitmap((View) chartInfo.chart, chartWidth, chartHeight);
                if (chartBitmap != null) {
                    String fileName = sanitizeFileName(chartInfo.title) + "_" + timestamp + ".png";
                    String path = saveChartBitmapWithMediaStore(chartBitmap, fileName, chartInfo.title, currentDate);
                    if (path != null) {
                        imagePaths.add(path);
                    }
                    chartBitmap.recycle();
                }
            }
        }
        
        return imagePaths;
    }
    
    /**
     * Exporta imágenes de gráficos usando sistema de archivos
     */
    private List<String> exportImagesWithFileSystem(List<ChartInfo> charts, String timestamp, File directory) throws IOException {
        List<String> imagePaths = new ArrayList<>();
        int chartWidth = 1200;
        int chartHeight = 800;
        String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(new Date());
        
        for (ChartInfo chartInfo : charts) {
            if (chartInfo.chart != null && chartInfo.chart instanceof View) {
                Bitmap chartBitmap = captureChartAsBitmap((View) chartInfo.chart, chartWidth, chartHeight);
                if (chartBitmap != null) {
                    String fileName = sanitizeFileName(chartInfo.title) + "_" + timestamp + ".png";
                    String path = saveChartBitmapWithFileSystem(chartBitmap, fileName, chartInfo.title, currentDate, directory);
                    if (path != null) {
                        imagePaths.add(path);
                    }
                    chartBitmap.recycle();
                }
            }
        }
        
        return imagePaths;
    }
    
    /**
     * Guarda bitmap con MediaStore
     */
    private String saveChartBitmapWithMediaStore(Bitmap chartBitmap, String fileName, String chartTitle, String currentDate) {
        try {
            int padding = 40;
            int titleHeight = 100;
            int width = chartBitmap.getWidth() + (padding * 2);
            int height = chartBitmap.getHeight() + titleHeight + (padding * 2);
            
            Bitmap finalBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(finalBitmap);
            canvas.drawColor(Color.WHITE);
            
            Paint paint = new Paint();
            paint.setAntiAlias(true);
            
            // Título
            paint.setTextSize(36);
            paint.setColor(context.getResources().getColor(R.color.primary));
            paint.setFakeBoldText(true);
            canvas.drawText(chartTitle, padding, padding + 50, paint);
            
            // Fecha
            paint.setTextSize(16);
            paint.setFakeBoldText(false);
            paint.setColor(Color.GRAY);
            canvas.drawText("Generado: " + currentDate, padding, padding + 80, paint);
            
            // Gráfico
            canvas.drawBitmap(chartBitmap, padding, titleHeight + padding, null);
            
            // Guardar con MediaStore
            ContentValues contentValues = new ContentValues();
            contentValues.put(MediaStore.Downloads.DISPLAY_NAME, fileName);
            contentValues.put(MediaStore.Downloads.MIME_TYPE, "image/png");
            contentValues.put(MediaStore.Downloads.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS + "/DroidTour");
            
            Uri uri = context.getContentResolver().insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues);
            if (uri != null) {
                FileOutputStream fos = (FileOutputStream) context.getContentResolver().openOutputStream(uri);
                if (fos != null) {
                    finalBitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
                    fos.close();
                    finalBitmap.recycle();
                    return uri.toString();
                }
            }
            finalBitmap.recycle();
        } catch (Exception e) {
            Log.e(TAG, "Error guardando gráfico con MediaStore: " + chartTitle, e);
        }
        return null;
    }
    
    /**
     * Guarda bitmap con sistema de archivos
     */
    private String saveChartBitmapWithFileSystem(Bitmap chartBitmap, String fileName, String chartTitle, 
                                                String currentDate, File directory) {
        try {
            int padding = 40;
            int titleHeight = 100;
            int width = chartBitmap.getWidth() + (padding * 2);
            int height = chartBitmap.getHeight() + titleHeight + (padding * 2);
            
            Bitmap finalBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(finalBitmap);
            canvas.drawColor(Color.WHITE);
            
            Paint paint = new Paint();
            paint.setAntiAlias(true);
            
            // Título
            paint.setTextSize(36);
            paint.setColor(context.getResources().getColor(R.color.primary));
            paint.setFakeBoldText(true);
            canvas.drawText(chartTitle, padding, padding + 50, paint);
            
            // Fecha
            paint.setTextSize(16);
            paint.setFakeBoldText(false);
            paint.setColor(Color.GRAY);
            canvas.drawText("Generado: " + currentDate, padding, padding + 80, paint);
            
            // Gráfico
            canvas.drawBitmap(chartBitmap, padding, titleHeight + padding, null);
            
            // Guardar archivo
            File file = new File(directory, fileName);
            FileOutputStream fos = new FileOutputStream(file);
            finalBitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
            fos.close();
            finalBitmap.recycle();
            
            return file.getAbsolutePath();
        } catch (Exception e) {
            Log.e(TAG, "Error guardando gráfico: " + chartTitle, e);
            return null;
        }
    }
    
    /**
     * Captura un gráfico como Bitmap
     */
    private Bitmap captureChartAsBitmap(View chartView, int width, int height) {
        if (chartView == null) return null;
        
        try {
            // Deshabilitar gestos temporalmente para captura limpia
            if (chartView instanceof LineChart) {
                LineChart chart = (LineChart) chartView;
                chart.setTouchEnabled(false);
                chart.setDragEnabled(false);
                chart.setScaleEnabled(false);
            } else if (chartView instanceof BarChart) {
                BarChart chart = (BarChart) chartView;
                chart.setTouchEnabled(false);
                chart.setDragEnabled(false);
                chart.setScaleEnabled(false);
            } else if (chartView instanceof PieChart) {
                PieChart chart = (PieChart) chartView;
                chart.setTouchEnabled(false);
                chart.setRotationEnabled(false);
            }
            
            // Usar dimensiones actuales del view
            int viewWidth = chartView.getWidth() > 0 ? chartView.getWidth() : width;
            int viewHeight = chartView.getHeight() > 0 ? chartView.getHeight() : height;
            
            if (viewWidth == 0 || viewHeight == 0) {
                chartView.measure(
                        View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY),
                        View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY)
                );
                viewWidth = chartView.getMeasuredWidth();
                viewHeight = chartView.getMeasuredHeight();
                chartView.layout(0, 0, viewWidth, viewHeight);
            }
            
            // Crear bitmap
            Bitmap bitmap = Bitmap.createBitmap(viewWidth, viewHeight, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            canvas.drawColor(Color.WHITE);
            chartView.draw(canvas);
            
            return bitmap;
        } catch (Exception e) {
            Log.e(TAG, "Error capturando gráfico", e);
            return null;
        } finally {
            // Restaurar gestos a valores por defecto (habilitados)
            try {
                if (chartView instanceof LineChart) {
                    LineChart chart = (LineChart) chartView;
                    chart.setTouchEnabled(true);
                    chart.setDragEnabled(true);
                    chart.setScaleEnabled(true);
                    chart.invalidate();
                } else if (chartView instanceof BarChart) {
                    BarChart chart = (BarChart) chartView;
                    chart.setTouchEnabled(true);
                    chart.setDragEnabled(true);
                    chart.setScaleEnabled(true);
                    chart.invalidate();
                } else if (chartView instanceof PieChart) {
                    PieChart chart = (PieChart) chartView;
                    chart.setTouchEnabled(true);
                    chart.setRotationEnabled(true);
                    chart.invalidate();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error restaurando estado del gráfico", e);
            }
        }
    }
    
    /**
     * Sanitiza nombre de archivo
     */
    private String sanitizeFileName(String title) {
        return title.replaceAll("[^a-zA-Z0-9_\\-]", "_");
    }
    
    /**
     * Clase para información de gráfico
     */
    public static class ChartInfo {
        public View chart;
        public String title;
        
        public ChartInfo(View chart, String title) {
            this.chart = chart;
            this.title = title;
        }
    }
    
    /**
     * Exporta solo imágenes de gráficos (sin PDF)
     */
    public void exportChartsAsImages(List<ChartInfo> charts, ExportImagesCallback callback) {
        try {
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ usar MediaStore
                List<String> imagePaths = exportImagesWithMediaStore(charts, timestamp);
                if (callback != null) {
                    callback.onSuccess(imagePaths);
                }
            } else {
                // Android 9 y anteriores usar directorio tradicional
                File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
                File droidTourDir = new File(downloadsDir, "DroidTour");
                if (!droidTourDir.exists() && !droidTourDir.mkdirs()) {
                    if (callback != null) {
                        callback.onError(new IOException("No se pudo crear el directorio de descarga"));
                    }
                    return;
                }
                List<String> imagePaths = exportImagesWithFileSystem(charts, timestamp, droidTourDir);
                if (callback != null) {
                    callback.onSuccess(imagePaths);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error exportando imágenes", e);
            if (callback != null) {
                callback.onError(e);
            }
        }
    }
    
    /**
     * Callback para exportación completa
     */
    public interface ExportCompleteCallback {
        void onSuccess(String pdfPath, List<String> imagePaths);
        void onError(Exception error);
    }
    
    /**
     * Callback para exportación de imágenes
     */
    public interface ExportImagesCallback {
        void onSuccess(List<String> imagePaths);
        void onError(Exception error);
    }
}
