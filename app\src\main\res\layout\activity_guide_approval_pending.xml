<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/white">

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_hourglass_empty"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="32dp"
        android:contentDescription="Esperando aprobación" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Registro en Revisión"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Tu registro como guía está en proceso de revisión. Recibirás una notificación cuando tu cuenta sea activada por el administrador."
        android:textSize="16sp"
        android:textColor="@color/gray"
        android:lineSpacingExtra="8sp"
        android:gravity="center"
        android:textAlignment="center"
        android:layout_marginBottom="48dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnLogout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Cerrar Sesión"
        android:layout_gravity="center_horizontal"
        app:backgroundTint="@color/primary"
        android:textColor="@color/white" />

</LinearLayout>