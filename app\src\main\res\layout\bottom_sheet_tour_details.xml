<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/bg_bottom_sheet_rounded"
    android:paddingTop="12dp"
    android:paddingBottom="0dp">

    <!-- Handler -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/bottom_sheet_handle"
        app:tint="#E0E0E0"
        android:layout_marginBottom="12dp" />

    <!-- HEADER -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingHorizontal="20dp"
        android:paddingBottom="12dp">

        <TextView
            android:id="@+id/tv_sheet_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Detalles del Tour"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#2C2C2C"
            tools:text="Tour Machu Picchu Express" />

        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            app:tint="#757575"
            android:contentDescription="Cerrar" />
    </LinearLayout>

    <!-- Scrollable content: ahora ocupa el espacio restante (weight) -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:maxHeight="650dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="20dp"
            android:paddingBottom="20dp">

            <!-- BADGE: No editable si tiene guía asignado -->
            <LinearLayout
                android:id="@+id/badge_edit_lock"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="#FFF3E0"
                android:padding="12dp"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp"
                android:layout_marginTop="4dp"
                android:backgroundTint="#FFF3E0"
                android:elevation="2dp"
                android:clipToPadding="false"
                android:paddingHorizontal="16dp">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:src="@drawable/ic_alert"
                    app:tint="#FB8C00"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@null" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Este tour no puede ser editado porque ya fue asignado o propuesto a un guía."
                    android:textColor="#FB8C00"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- IMAGEN PRINCIPAL -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="180dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                android:layout_marginBottom="20dp">

                <ImageView
                    android:id="@+id/iv_main_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:background="#E0E0E0"
                    tools:src="@drawable/ic_empty_box"
                    android:contentDescription="@null" />
            </com.google.android.material.card.MaterialCardView>


            <!-- CARD: Información Básica -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_info"
                            app:tint="#2196F3"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@null" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Información Básica"
                            android:textColor="#2C2C2C"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <!-- Divisor -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <!-- Nombre -->
                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_title"
                        tools:text="Machu Picchu Express" />

                    <!-- Categoría -->
                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_category"
                        tools:text="Aventura" />

                    <!-- Precio -->
                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_price"
                        tools:text="S/ 850" />

                    <!-- Duración -->
                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_duration"
                        tools:text="3 días" />


                    <!-- Descripción -->
                    <TextView
                        android:text="Descripción"
                        android:textStyle="bold"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#2C2C2C"
                        android:textSize="15sp"
                        android:layout_marginTop="12dp" />

                    <TextView
                        android:id="@+id/tv_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="14sp"
                        android:textColor="#616161"
                        android:layout_marginTop="6dp"
                        tools:text="Un recorrido inolvidable..." />

                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- CARD: Fechas y disponibilidad -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_calendar_form"
                            app:tint="#9C27B0"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@null" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Fechas y Disponibilidad"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_start_date"
                        tools:text="15/12/2024" />

                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_end_date"
                        tools:text="18/12/2024" />

                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_capacity"
                        tools:text="15 personas" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- CARD: Servicios -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_services"
                            app:tint="#E91E63"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@null" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Servicios Incluidos"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <TextView
                        android:id="@+id/tv_services_included"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#616161"
                        android:textSize="14sp"
                        tools:text="Desayuno, Transporte, Guía profesional" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- CARD: Servicios No Incluidos -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:src="@drawable/ic_services_excluded"
                            app:tint="#FF5722"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@null" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Servicios No Incluidos"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <TextView
                        android:id="@+id/tv_services_not_included"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#616161"
                        android:textSize="14sp"
                        tools:text="Bebidas alcohólicas, Propinas, Actividades extra" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- CARD: Ubicaciones -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:src="@drawable/ic_location"
                            app:tint="#4CAF50"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@null" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Ubicaciones del Tour"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_locations"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_tour_location" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- CARD: Idiomas -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:src="@drawable/ic_language"
                            app:tint="#3F51B5"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@null" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Idiomas Disponibles"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <TextView
                        android:id="@+id/tv_languages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#616161"
                        android:textSize="14sp"
                        tools:text="Español, Inglés, Francés" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- CARD: Encuentro -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:src="@drawable/ic_meeting_point"
                            app:tint="#795548"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@null" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Información de Encuentro"
                            android:textStyle="bold"
                            android:textSize="16sp"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="12dp" />

                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_meeting_point"
                        tools:text="Plaza de Armas de Cusco" />

                    <include layout="@layout/item_info_row"
                        android:id="@+id/row_meeting_time"
                        tools:text="08:30" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>
    <!-- BOTÓN FIJO AL FINAL (NO SCROLEA) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="#FFFFFF"
        android:elevation="8dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_edit_tour"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:text="Editar Tour"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="12dp"
            app:backgroundTint="@color/primary"
            android:textColor="@color/white"
            app:icon="@drawable/ic_edit"
            app:iconTint="@color/white"
            app:iconGravity="textStart"
            app:iconSize="20dp" />
    </LinearLayout>

</LinearLayout>
