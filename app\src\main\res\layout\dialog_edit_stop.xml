<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- T<PERSON><PERSON><PERSON> de la parada -->
    <TextView
        android:id="@+id/tv_stop_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Editar Parada"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/primary"
        android:layout_marginBottom="16dp" />

    <!-- Nombre de la ubicación (solo lectura) -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ubicación"
        android:textSize="12sp"
        android:textColor="#757575"
        android:layout_marginBottom="4dp" />

    <TextView
        android:id="@+id/tv_location_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Nombre de la ubicación"
        android:textSize="16sp"
        android:textColor="#2C2C2C"
        android:textStyle="bold"
        android:padding="12dp"
        android:background="@drawable/rounded_background_gray"
        android:layout_marginBottom="16dp" />

    <!-- Duración de la parada -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_stop_duration"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Duración en minutos"
        app:startIconDrawable="@drawable/ic_time"
        app:startIconTint="@color/primary"
        app:boxCornerRadiusTopStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusBottomEnd="8dp"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_stop_duration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Descripción -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_stop_description"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Descripción"
        app:startIconDrawable="@drawable/ic_description"
        app:startIconTint="@color/primary"
        app:boxCornerRadiusTopStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusBottomEnd="8dp"
        app:counterEnabled="true"
        app:counterMaxLength="200"
        android:layout_marginBottom="24dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_stop_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:lines="3"
            android:maxLength="200" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Botones -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cancelar"
            android:textColor="#757575" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Guardar"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>

