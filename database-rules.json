{
  "rules": {
    "conversations": {
      "$conversationId": {
        ".read": "auth != null && (
          data.child('clientId').val() == auth.uid ||
          data.child('companyId').val() == auth.uid ||
          root.child('users').child(auth.uid).child('companyId').val() == data.child('companyId').val()
        )",
        ".write": "auth != null && (
          data.child('clientId').val() == auth.uid ||
          root.child('users').child(auth.uid).child('companyId').val() == data.child('companyId').val()
        )",
        "messages": {
          "$messageId": {
            ".read": "auth != null && (
              parent().parent().child('clientId').val() == auth.uid ||
              parent().parent().child('companyId').val() == auth.uid ||
              root.child('users').child(auth.uid).child('companyId').val() == parent().parent().child('companyId').val()
            )",
            ".write": "auth != null && (
              newData.child('senderId').val() == auth.uid ||
              (newData.child('status').val() == 'DELIVERED' || newData.child('status').val() == 'READ')
            )"
          }
        }
      }
    },
    "user_presence": {
      "$userId": {
        ".read": "auth != null",
        ".write": "auth != null && $userId == auth.uid"
      }
    },
    "conversation_index": {
      "$userType": {
        "$userId": {
          ".read": "auth != null && $userId == auth.uid",
          ".write": "auth != null && $userId == auth.uid"
        }
      }
    },
    "users": {
      "$userId": {
        ".read": "auth != null",
        ".write": "auth != null && $userId == auth.uid"
      }
    }
  }
}

