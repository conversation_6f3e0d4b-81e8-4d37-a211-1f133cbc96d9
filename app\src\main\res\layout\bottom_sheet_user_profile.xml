<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_bottom_sheet_rounded"
    android:clipToPadding="false"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <!-- Handle del bottom sheet -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bottom_sheet_handle"
        android:backgroundTint="#E0E0E0" />

    <!-- Header con botón cerrar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingHorizontal="20dp"
        android:paddingTop="8dp"
        android:paddingBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Perfil de Usuario"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#2C2C2C" />

        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:tint="#757575"
            android:contentDescription="Cerrar" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="600dp"
        android:fillViewport="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="20dp"
            android:paddingBottom="20dp">

            <!-- Sección: Avatar y datos básicos -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Avatar y nombre -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            app:cardCornerRadius="32dp"
                            app:cardElevation="0dp"
                            app:strokeWidth="2dp"
                            app:strokeColor="@color/primary">

                            <FrameLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/primary_light">

                                <ImageView
                                    android:id="@+id/iv_user_avatar"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:contentDescription="Avatar"
                                    tools:src="@drawable/ic_avatar_24" />

                                <TextView
                                    android:id="@+id/tv_avatar_initial"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/primary"
                                    tools:text="MG" />
                            </FrameLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:layout_marginStart="16dp">

                            <TextView
                                android:id="@+id/tv_user_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="María González"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C"
                                android:maxLines="1"
                                android:ellipsize="end"
                                tools:text="María González López" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chip_user_role"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Cliente"
                                android:textSize="12sp"
                                android:layout_marginTop="4dp"
                                app:chipBackgroundColor="@color/primary_light"
                                android:textColor="@color/primary"
                                app:chipStrokeWidth="0dp"
                                app:chipMinHeight="28dp"
                                app:chipCornerRadius="8dp"
                                tools:text="Guía Turístico" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Divisor -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <!-- Detalles del usuario -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Email -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_email"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Email" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Email"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <TextView
                                android:id="@+id/tv_user_email"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="<EMAIL>"
                                android:textSize="14sp"
                                android:textColor="#2C2C2C"
                                android:textStyle="bold"
                                android:maxLines="1"
                                android:ellipsize="end"
                                tools:text="<EMAIL>" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#F5F5F5" />

                        <!-- Teléfono -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_phone"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Teléfono" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Teléfono"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <TextView
                                android:id="@+id/tv_user_phone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="+51 999 999 999"
                                android:textSize="14sp"
                                android:textColor="#2C2C2C"
                                android:textStyle="bold"
                                tools:text="+51 987 654 321" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#F5F5F5" />

                        <!-- Fecha de registro -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_calendar_form"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Fecha" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Miembro desde"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <TextView
                                android:id="@+id/tv_registration_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="15 Dic 2024"
                                android:textSize="14sp"
                                android:textColor="#2C2C2C"
                                android:textStyle="bold"
                                tools:text="Nov 2024" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#F5F5F5" />

                        <!-- Estado -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingVertical="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:src="@drawable/ic_info"
                                android:tint="#757575"
                                android:layout_marginEnd="12dp"
                                android:contentDescription="Estado" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Estado"
                                android:textSize="14sp"
                                android:textColor="#757575" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chip_user_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Activo"
                                android:textSize="12sp"
                                app:chipBackgroundColor="#E8F5E9"
                                android:textColor="#4CAF50"
                                app:chipStrokeWidth="0dp"
                                app:chipMinHeight="28dp"
                                app:chipCornerRadius="8dp"
                                tools:text="Activo" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Idiomas (visible solo para guías) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_languages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_language"
                            android:tint="#FF9800"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Idiomas" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Idiomas"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <!-- ChipGroup de idiomas -->
                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_languages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:chipSpacingHorizontal="8dp"
                        app:chipSpacingVertical="8dp"
                        app:singleLine="false" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Estadísticas -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_statistics"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_stats"
                            android:tint="#4CAF50"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Estadísticas" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Estadísticas"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="12dp" />

                    <!-- Stats grid -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:baselineAligned="false">

                        <!-- Tours -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tv_tours_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="12"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="#2196F3"
                                tools:text="12" />

                            <TextView
                                android:id="@+id/tv_stat_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tours"
                                android:textSize="13sp"
                                android:textColor="#757575"
                                android:gravity="center"
                                android:layout_marginTop="4dp"
                                tools:text="Tours" />
                        </LinearLayout>

                        <!-- Rating -->
                        <LinearLayout
                            android:id="@+id/layout_rating"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tv_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="4.8"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="#FF9800"
                                tools:text="4.8" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Rating"
                                android:textSize="13sp"
                                android:textColor="#757575"
                                android:gravity="center"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <!-- Año -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/tv_member_year"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="2024"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="#9C27B0"
                                tools:text="2024" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Desde"
                                android:textSize="13sp"
                                android:textColor="#757575"
                                android:gravity="center"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Botones de acción -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_edit_profile"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="Editar"
                    android:textSize="14sp"
                    app:icon="@drawable/ic_edit"
                    app:iconTint="@color/primary"
                    app:iconSize="18dp"
                    app:iconGravity="textStart"
                    app:cornerRadius="8dp"
                    app:strokeColor="@color/primary"
                    android:textColor="@color/primary"
                    android:layout_marginEnd="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_send_message"
                    style="@style/Widget.Material3.Button"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="Mensaje"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:icon="@drawable/ic_message"
                    app:iconSize="18dp"
                    app:iconGravity="textStart"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/primary" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>