# Configuración de Secrets para el proyecto

Este archivo explica cómo usar el `Secrets Gradle Plugin` para almacenar la API key de Google Maps localmente y en CI.

Resumen rápido
- El proyecto usa el plugin `com.google.android.libraries.mapsplatform.secrets-gradle-plugin`.
- <PERSON><PERSON><PERSON> proveer la clave vía:
  1. Variable de entorno `MAPS_API_KEY` (recomendada para CI)
  2. Propiedad de Gradle `MAPS_API_KEY` (útil si tu entorno de desarrollo la expone)
  3. Archivo `app/secrets.properties` (local, NO comitear)

Local (Windows - cmd.exe)
1. Copia `app/secrets.properties.template` a `app/secrets.properties`.
2. Edita `app/secrets.properties` y pon:
   MAPS_API_KEY=YOUR_MAPS_API_KEY_HERE

Alternativamente, para la sesión actual en cmd.exe:

```
set MAPS_API_KEY=YOUR_MAPS_API_KEY_HERE
```

Para persistir la variable para nuevos inicios de sesión (Windows):

```
setx MAPS_API_KEY "YOUR_MAPS_API_KEY_HERE"
```

macOS / Linux (bash/zsh):

```
export MAPS_API_KEY=YOUR_MAPS_API_KEY_HERE
```

GitHub Actions (ejemplo de job):
# Template para app/secrets.properties (NO comitear)
```yaml
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: '17'
      - name: Build
        env:
          MAPS_API_KEY: ${{ secrets.MAPS_API_KEY }}
        run: ./gradlew assembleDebug
```

Buenas prácticas
- Restringe la clave en Google Cloud Console (paquete Android + SHA-1) y por APIs.
- Nunca comitees `app/secrets.properties` ni `local.properties`.
- Considera rotar la clave si alguien la filtra.

Si quieres, puedo añadir una verificación que haga fallar el build de `release` si la clave está vacía o automatizar la generación de `app/secrets.properties` desde un script seguro del equipo.
# Copia este archivo a app/secrets.properties y añade tu API key.
# Formato: clave=valor
MAPS_API_KEY=YOUR_MAPS_API_KEY_HERE

