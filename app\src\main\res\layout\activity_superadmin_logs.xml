<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="@string/system_logs"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Search Bar -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_search_logs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:hint="Buscar en logs"
        app:startIconDrawable="@android:drawable/ic_menu_search"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search_logs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Log Level Filter -->
    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group_log_levels"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/til_search_logs">

        <com.google.android.material.chip.Chip
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Todos"
            android:checkable="true"
            android:checked="true"
            app:chipBackgroundColor="@color/primary" />

        <com.google.android.material.chip.Chip
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Error"
            android:checkable="true"
            app:chipBackgroundColor="@color/red" />

        <com.google.android.material.chip.Chip
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Warning"
            android:checkable="true"
            app:chipBackgroundColor="@color/orange" />

        <com.google.android.material.chip.Chip
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Info"
            android:checkable="true"
            app:chipBackgroundColor="@color/green" />

    </com.google.android.material.chip.ChipGroup>

    <!-- Logs List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_logs"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/chip_group_log_levels"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Logs Placeholder -->
    <LinearLayout
        android:id="@+id/layout_logs_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="@id/rv_logs"
        app:layout_constraintBottom_toBottomOf="@id/rv_logs"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@android:drawable/ic_menu_info_details"
            android:tint="@color/gray"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Logs del Sistema"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Aquí se mostrarán los eventos del sistema"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:gravity="center"
            android:layout_marginTop="8dp" />

        <!-- Sample Log Entries -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="24dp"
            android:background="@color/light_gray"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="[INFO] Usuario <EMAIL> inició sesión"
                android:textSize="12sp"
                android:textColor="@color/green"
                android:fontFamily="monospace"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="[WARN] Intento de acceso fallido desde IP *************"
                android:textSize="12sp"
                android:textColor="@color/orange"
                android:fontFamily="monospace"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="[ERROR] Fallo en conexión a base de datos"
                android:textSize="12sp"
                android:textColor="@color/red"
                android:fontFamily="monospace" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
