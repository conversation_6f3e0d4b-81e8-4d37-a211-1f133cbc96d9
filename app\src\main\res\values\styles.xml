<resources>
    <style name="CustomToolbarTitle" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">22sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="CircleImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="droidtour">
        <item name="android:textSize">60sp</item>
        <item name="android:textColor">#006CE4</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="droidtour.white">
    <item name="android:textSize">60sp</item>
    <item name="android:textColor">#FFFFFF</item>
    <item name="android:textStyle">bold</item>
    </style>

    <style name="ToolbarTitleBold" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">22sp</item>
    </style>

    <style name="CCPDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@drawable/dialog_rounded_background</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:windowMinWidthMajor">85%</item>
        <item name="android:windowMinWidthMinor">85%</item>
    </style>

    <style name="Toolbar.TitleText2" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="fontFamily">@font/bricolage_grotesque_medium</item>
        <item name="android:textSize">26sp</item>


    </style>

    <!-- Estilo para ocultar el título expandido (texto transparente) -->
    <style name="CollapsingExpandedTitle" parent="TextAppearance.AppCompat.Title">
        <item name="android:textColor">@android:color/transparent</item>
        <item name="android:textSize">0sp</item>
    </style>

    <!-- Estilo para las pestañas de TabLayout -->
    <style name="TabTextStyle" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <!-- No fijamos color aquí: el TabLayout usa app:tabTextColor y app:tabSelectedTextColor -->
    </style>

    <style name="RatingBar" parent="Widget.AppCompat.RatingBar">
        <item name="colorAccent">@color/orange</item>
        <item name="colorControlNormal">@color/light_gray</item>
    </style>

    <style name="Toolbar.TitleText3" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
    <item name="fontFamily">@font/bricolage_grotesque_medium</item>
    <item name="android:textSize">20sp</item>
    </style>

    <style name="Toolbar.SubTitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    <item name="fontFamily">@font/bricolage_grotesque</item>
    </style>

    <style name="TextAppearance.AppCompat.MediumNeo" parent="TextAppearance.AppCompat.Medium">
        <item name="fontFamily">@font/bricolage_grotesque_medium</item>
    </style>


    <style name="ExpandedAppearance" parent="TextAppearance.Design.CollapsingToolbar.Expanded">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:visibility">gone</item> <!-- ¡Importante! -->
        <item name="android:textColor">@android:color/transparent</item>
    </style>

    <style name="CollapsedAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
    </style>



</resources>
