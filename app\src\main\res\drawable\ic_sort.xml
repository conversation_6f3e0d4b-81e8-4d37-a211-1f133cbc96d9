<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M16.069,15H7.931C7.325,15 7.023,15 6.882,15.12C6.761,15.224 6.696,15.38 6.709,15.539C6.723,15.723 6.937,15.937 7.366,16.366L11.434,20.434C11.632,20.632 11.731,20.731 11.845,20.768C11.946,20.801 12.054,20.801 12.154,20.768C12.269,20.731 12.368,20.632 12.566,20.434L16.634,16.366C17.063,15.937 17.277,15.723 17.291,15.539C17.304,15.38 17.239,15.224 17.118,15.12C16.977,15 16.674,15 16.069,15Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M7.931,9H16.069C16.674,9 16.977,9 17.118,8.88C17.239,8.776 17.304,8.62 17.291,8.461C17.277,8.277 17.063,8.063 16.634,7.634L12.566,3.566C12.368,3.368 12.269,3.269 12.154,3.232C12.054,3.199 11.946,3.199 11.845,3.232C11.731,3.269 11.632,3.368 11.434,3.566L7.366,7.634C6.937,8.063 6.723,8.277 6.709,8.461C6.696,8.62 6.761,8.776 6.882,8.88C7.023,9 7.325,9 7.931,9Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:strokeLineCap="round"/>
</vector>
