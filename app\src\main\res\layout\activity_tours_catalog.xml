<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- AppBarLayout con CollapsingToolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="@color/primary"
            app:expandedTitleMarginStart="16dp"
            app:expandedTitleMarginBottom="16dp"
            app:title="Tours Disponibles"
            app:collapsedTitleTextAppearance="@style/CollapsedAppearance"
            app:expandedTitleTextAppearance="@style/ExpandedAppearance"
            app:expandedTitleGravity="bottom">

            <!-- Header expandido de la empresa -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@color/primary"
                android:paddingTop="?attr/actionBarSize"
                android:paddingBottom="24dp"
                app:layout_collapseMode="parallax">

                <!-- Info de la empresa -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <!-- Logo de la empresa -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/white">

                            <ImageView
                                android:id="@+id/iv_company_logo"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerInside"
                                android:padding="12dp"
                                android:contentDescription="Logo empresa"
                                tools:src="@drawable/ic_company" />

                            <TextView
                                android:id="@+id/tv_company_initial"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:textSize="28sp"
                                android:textStyle="bold"
                                android:textColor="@color/primary"
                                android:visibility="gone"
                                tools:text="LA"
                                tools:visibility="visible" />
                        </FrameLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <!-- Nombre y stats -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_company_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Lima Adventure Tours"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            android:maxLines="2"
                            android:ellipsize="end"
                            tools:text="Lima Adventure Tours" />

                        <!-- Rating y reseñas -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginTop="8dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:src="@drawable/ic_star"
                                app:tint="#FFD700"
                                android:contentDescription="Rating" />

                            <TextView
                                android:id="@+id/tv_company_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="4.8"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                android:textColor="@color/white"
                                android:layout_marginStart="4dp"
                                tools:text="4.8" />

                            <TextView
                                android:id="@+id/tv_reviews_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="(245 reseñas)"
                                android:textSize="13sp"
                                android:textColor="#E8E8E8"
                                android:layout_marginStart="6dp"
                                tools:text="(245 reseñas)" />
                        </LinearLayout>

                        <!-- Ubicación -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginTop="6dp">

                            <ImageView
                                android:layout_width="14dp"
                                android:layout_height="14dp"
                                android:src="@drawable/ic_location"
                                app:tint="#E8E8E8"
                                android:contentDescription="Ubicación" />

                            <TextView
                                android:id="@+id/tv_company_location"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Lima, Perú"
                                android:textSize="13sp"
                                android:textColor="#E8E8E8"
                                android:layout_marginStart="4dp"
                                tools:text="Lima, Perú" />
                        </LinearLayout>
                    </LinearLayout>


                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_profile"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:layout_marginHorizontal="20dp"

                        android:backgroundTint="@color/white"
                        android:text="Ver Perfil"
                        android:textColor="@color/primary"
                        android:textSize="16sp"
                        app:cornerRadius="8dp" />




                </LinearLayout>

                <!-- Stats horizontales -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/bg_detail_item"
                    android:padding="16dp"
                    android:elevation="2dp">

                    <!-- Total tours -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tv_tours_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="12"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="#2196F3"
                            tools:text="12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Tours"
                            android:textSize="12sp"
                            android:textColor="#757575"
                            android:layout_marginTop="2dp" />
                    </LinearLayout>

                    <!-- Divisor -->
                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:background="#E0E0E0"
                        android:layout_marginHorizontal="16dp" />

                    <!-- Precio desde -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tv_price_from"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="S/ 150"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="#4CAF50"
                            tools:text="S/ 150" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Desde"
                            android:textSize="12sp"
                            android:textColor="#757575"
                            android:layout_marginTop="2dp" />
                    </LinearLayout>

                    <!-- Divisor -->
                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:background="#E0E0E0"
                        android:layout_marginHorizontal="16dp" />

                    <!-- Experiencia -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/tv_experience_years"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="5+"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="#FF9800"
                            tools:text="5+" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Años"
                            android:textSize="12sp"
                            android:textColor="#757575"
                            android:layout_marginTop="2dp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- Toolbar colapsable -->
            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@android:color/transparent"
                app:titleTextColor="@color/white"
                app:navigationIcon="?attr/homeAsUpIndicator"
                app:navigationIconTint="@color/white"
                app:layout_collapseMode="pin" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Contenido principal -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="16dp">

            <!-- Búsqueda y filtros -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Barra de búsqueda -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_search"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Buscar tour..."
                        app:startIconDrawable="@drawable/ic_search_form"
                        app:startIconTint="@color/primary"
                        app:endIconMode="clear_text"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_search"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:imeOptions="actionSearch"
                            android:inputType="text"
                            android:textSize="16sp"
                            android:textColor="#000000" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Botones de filtro y ordenar -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_filters"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:text="Filtros"
                            android:textSize="14sp"
                            app:icon="@drawable/ic_filter"
                            app:iconTint="@color/primary"
                            app:iconSize="18dp"
                            app:iconGravity="textStart"
                            app:strokeColor="@color/primary"
                            android:textColor="@color/primary"
                            app:cornerRadius="8dp"
                            android:layout_marginEnd="8dp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_sort"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:text="Ordenar"
                            android:textSize="14sp"
                            app:icon="@drawable/ic_sort"
                            app:iconTint="@color/primary"
                            app:iconSize="18dp"
                            app:iconGravity="textStart"
                            app:strokeColor="@color/primary"
                            android:textColor="@color/primary"
                            app:cornerRadius="8dp"
                            android:layout_marginStart="8dp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Filtros rápidos con chips -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="8dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Filtros rápidos"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="#757575"
                    android:layout_marginBottom="8dp" />

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none">

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:singleLine="true"
                        app:chipSpacingHorizontal="8dp">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_all"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Todos"
                            android:checkable="true"
                            android:checked="true"
                            app:chipBackgroundColor="@color/chip_bg_selector"
                            app:checkedIconVisible="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_adventure"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🏔️ Aventura"
                            android:checkable="true"
                            app:chipBackgroundColor="@color/chip_bg_selector"
                            app:checkedIconVisible="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_culture"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🏛️ Cultural"
                            android:checkable="true"
                            app:chipBackgroundColor="@color/chip_bg_selector"
                            app:checkedIconVisible="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_nature"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🌿 Naturaleza"
                            android:checkable="true"
                            app:chipBackgroundColor="@color/chip_bg_selector"
                            app:checkedIconVisible="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_food"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🍽️ Gastronómico"
                            android:checkable="true"
                            app:chipBackgroundColor="@color/chip_bg_selector"
                            app:checkedIconVisible="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_best_price"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💰 Mejor precio"
                            android:checkable="true"
                            app:chipBackgroundColor="@color/chip_bg_selector"
                            app:checkedIconVisible="true" />

                        <!-- Filtro por duración (usado desde ToursCatalogActivity) -->
                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_duration"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⏱️ Duración"
                            android:checkable="true"
                            app:chipBackgroundColor="@color/chip_bg_selector"
                            app:checkedIconVisible="true" />
                    </com.google.android.material.chip.ChipGroup>
                </HorizontalScrollView>
            </LinearLayout>

            <!-- Contador de resultados -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_results_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="12 tours encontrados"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C"
                    tools:text="12 tours encontrados" />

                <!-- Switch de vista (lista/grid) -->
                <ImageButton
                    android:id="@+id/btn_view_mode"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_view_grid"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:tint="#757575"
                    android:contentDescription="Cambiar vista"
                    android:visibility="gone"/>
            </LinearLayout>

            <!-- RecyclerView de tours -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_tours"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="8dp"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                tools:itemCount="5" />

            <!-- Empty state -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="48dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@drawable/ic_search"
                    android:tint="#BDBDBD"
                    android:contentDescription="Sin tours" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No se encontraron tours"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#757575"
                    android:layout_marginTop="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Intenta ajustar tus filtros de búsqueda"
                    android:textSize="14sp"
                    android:textColor="#9E9E9E"
                    android:gravity="center"
                    android:layout_marginTop="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_clear_filters"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Limpiar filtros"
                    android:layout_marginTop="16dp"
                    app:cornerRadius="8dp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</androidx.coordinatorlayout.widget.CoordinatorLayout>