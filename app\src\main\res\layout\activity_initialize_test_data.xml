<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center">

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_firebase"
        android:layout_marginBottom="24dp"
        android:contentDescription="Firebase Icon"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Inicializar Datos de Prueba"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Esta herramienta creará datos de prueba en Firestore.\nSelecciona el tipo de datos que deseas inicializar:"
        android:textAlignment="center"
        android:layout_marginBottom="32dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_initialize_client"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Inicializar Datos de Cliente"
        android:paddingHorizontal="32dp"
        android:layout_marginBottom="16dp"
        app:cornerRadius="8dp"
        app:icon="@android:drawable/ic_menu_myplaces"
        app:iconGravity="textStart"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_initialize_guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Inicializar Datos de Guía"
        android:paddingHorizontal="32dp"
        app:cornerRadius="8dp"
        app:icon="@android:drawable/ic_menu_compass"
        app:iconGravity="textStart"
        style="@style/Widget.Material3.Button.TonalButton"/>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text=""
        android:textAlignment="center"
        android:textSize="14sp"/>

</LinearLayout>

