<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="6dp">

    <!-- ================= MENSAJE USUARIO (DERECHA) ================= -->
    <LinearLayout
        android:id="@+id/layout_user_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginBottom="6dp"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="64dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="18dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/primary">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Imagen adjunta -->
                    <ImageView
                        android:id="@+id/iv_user_message_image"
                        android:layout_width="200dp"
                        android:layout_height="200dp"
                        android:scaleType="centerCrop"
                        android:visibility="gone"
                        android:background="#E0E0E0" />

                    <!-- PDF adjunto -->
                    <LinearLayout
                        android:id="@+id/layout_user_pdf"
                        android:layout_width="200dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#E0E0E0"
                        android:gravity="center_vertical"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_pdf"
                            android:tint="#E53935"
                            android:layout_marginEnd="8dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_user_pdf_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="documento.pdf"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="@color/white"
                                android:maxLines="1"
                                android:ellipsize="end" />

                            <TextView
                                android:id="@+id/tv_user_pdf_size"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="2.5 MB"
                                android:textSize="10sp"
                                android:textColor="@color/white"
                                android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Texto del mensaje -->
                    <TextView
                        android:id="@+id/tv_user_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Hola, tengo una consulta sobre el tour"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:paddingStart="14dp"
                        android:paddingEnd="14dp"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:maxWidth="280dp"
                        android:visibility="gone" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Hora + estado -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end"
                android:layout_marginTop="2dp"
                android:layout_marginEnd="6dp">

                <TextView
                    android:id="@+id/tv_user_message_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:31 AM"
                    android:textSize="10sp"
                    android:textColor="@color/gray" />

                <!-- Placeholder estado leído -->
                <ImageView
                    android:id="@+id/iv_message_status"
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginStart="4dp"
                    android:src="@drawable/ic_check_double"
                    android:tint="@color/gray" />
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <!-- ================= MENSAJE EMPRESA (IZQUIERDA) ================= -->
    <LinearLayout
        android:id="@+id/layout_company_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="start"
        android:layout_marginBottom="6dp"
        android:visibility="gone">

        <!-- Avatar empresa -->
        <ImageView
            android:id="@+id/iv_company_avatar"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_avatar_24"
            android:background="@drawable/profile_image_circle"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="64dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="18dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Imagen adjunta -->
                    <ImageView
                        android:id="@+id/iv_company_message_image"
                        android:layout_width="200dp"
                        android:layout_height="200dp"
                        android:scaleType="centerCrop"
                        android:visibility="gone"
                        android:background="#E0E0E0" />

                    <!-- PDF adjunto -->
                    <LinearLayout
                        android:id="@+id/layout_company_pdf"
                        android:layout_width="200dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp"
                        android:background="#E0E0E0"
                        android:gravity="center_vertical"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_pdf"
                            android:tint="#E53935"
                            android:layout_marginEnd="8dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_company_pdf_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="documento.pdf"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C"
                                android:maxLines="1"
                                android:ellipsize="end" />

                            <TextView
                                android:id="@+id/tv_company_pdf_size"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="2.5 MB"
                                android:textSize="10sp"
                                android:textColor="#757575"
                                android:layout_marginTop="2dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Texto del mensaje -->
                    <TextView
                        android:id="@+id/tv_company_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¡Hola! Claro, estaremos encantados de ayudarte"
                        android:textColor="#2C2C2C"
                        android:textSize="14sp"
                        android:paddingStart="14dp"
                        android:paddingEnd="14dp"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:maxWidth="280dp"
                        android:visibility="gone" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/tv_company_message_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10:30 AM"
                android:textSize="10sp"
                android:textColor="@color/gray"
                android:layout_marginTop="2dp"
                android:layout_marginStart="8dp" />

        </LinearLayout>
    </LinearLayout>

</LinearLayout>
