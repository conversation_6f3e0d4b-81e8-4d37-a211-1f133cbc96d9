<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="DroidTour"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleCentered="true"
        app:titleTextColor="@color/white"
        app:menu="@menu/top_app_bar_general"
        app:navigationIcon="?attr/homeAsUpIndicator"
        app:navigationIconTint="@color/white"
        app:layout_constraintTop_toTopOf="parent" />



    <!-- <PERSON><PERSON> -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_search"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Buscar usuario"
        app:startIconDrawable="@drawable/ic_search_form"
        android:layout_margin="16dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:startIconTint="@color/primary"
        app:endIconMode="clear_text"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary"
        app:boxCornerRadiusTopStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusBottomEnd="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:textSize="16sp"
            android:textColor="#000000" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Filter Tabs -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_user_types"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintTop_toBottomOf="@id/til_search">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/todos" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tour_admin" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tour_guide" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/client" />

    </com.google.android.material.tabs.TabLayout>

    <!-- Users List dentro de SwipeRefreshLayout -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/tab_user_types"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_users"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="8dp"
            android:padding="16dp" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Loading indicator (material) -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/loading_indicator"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toBottomOf="@id/tab_user_types"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_empty_box"
            app:tint="@color/gray"
            android:contentDescription="@string/no_users_image"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_users"
            android:textSize="18sp"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

    </LinearLayout>
    <!-- FAB para agregar administrador (solo visible en pestaña ADMIN) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_admin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        android:contentDescription="Agregar administrador"
        android:src="@drawable/ic_add"
        app:backgroundTint="@color/primary"
        app:tint="@color/white"
        app:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
