<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Reseñas del Tour"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert" />

    <!-- Scrollable Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Tour Info Header -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="City Tour Lima Centro Histórico"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/black" />

                    <TextView
                        android:id="@+id/tv_total_reviews"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="10 reseñas encontradas"
                        android:textSize="14sp"
                        android:textColor="@color/gray"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Filter Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                android:paddingVertical="8dp">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:singleLine="false"
                    app:chipSpacingHorizontal="8dp"
                    app:chipSpacingVertical="8dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_all"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Todas"
                        android:checkable="true"
                        android:checked="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_best_rating"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Mejor Calificación"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_worst_rating"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Menor Calificación"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                </com.google.android.material.chip.ChipGroup>

            </LinearLayout>

            <!-- Reviews Container -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Reviews List -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_reviews"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="16dp"
                        android:paddingTop="16dp"
                        android:paddingEnd="16dp"
                        android:paddingBottom="16dp"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
