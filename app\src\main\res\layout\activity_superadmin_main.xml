<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Toolbar - MISMO ESTILO QUE TU DISEÑO ORIGINAL -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="DroidTour"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:menu="@menu/top_app_bar_general"
            app:navigationIcon="@android:drawable/ic_menu_sort_alphabetically"
            app:layout_constraintTop_toTopOf="parent">


        </com.google.android.material.appbar.MaterialToolbar>

        <!-- Tabs para diferentes períodos -->
        <LinearLayout
            android:id="@+id/tabs_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/primary"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/primary"
                app:tabTextColor="@android:color/white"
                app:tabSelectedTextColor="@android:color/white"
                app:tabIndicatorColor="@android:color/white"
                app:tabIndicatorHeight="3dp"
                app:tabMode="fixed"
                app:tabGravity="fill"
                app:tabTextAppearance="@style/TextAppearance.AppCompat.MediumNeo">

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Día" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Semana" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mes" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Año" />

            </com.google.android.material.tabs.TabLayout>

            <!-- Selectores de fecha según el período -->
            <LinearLayout
                android:id="@+id/date_selection_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:gravity="center"
                android:background="@color/primary"
                android:visibility="visible">

                <!-- Selector para Día y Semana (calendario) -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_select_date"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:text="Seleccionar día"
                    android:textColor="@android:color/white"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:minHeight="56dp"
                    app:icon="@drawable/ic_calendar_form"
                    app:iconTint="@android:color/white"
                    app:iconGravity="textStart"
                    app:iconPadding="12dp"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    app:strokeColor="@android:color/white"
                    app:strokeWidth="2dp"
                    app:cornerRadius="12dp"
                    android:visibility="gone" />

                <!-- Selectores para Mes (mes y año) -->
                <LinearLayout
                    android:id="@+id/month_selection_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:visibility="gone">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_month"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="Mes"
                        android:minHeight="56dp"
                        app:boxStrokeColor="@color/white_stroke_color"
                        app:hintTextColor="@color/white_hint_color"
                        app:boxCornerRadiusTopStart="12dp"
                        app:boxCornerRadiusTopEnd="12dp"
                        app:boxCornerRadiusBottomStart="12dp"
                        app:boxCornerRadiusBottomEnd="12dp"
                        app:boxStrokeWidth="2dp"
                        app:boxStrokeWidthFocused="2dp"
                        app:boxBackgroundColor="@android:color/transparent"
                        app:startIconTint="@android:color/white"
                        app:endIconTint="@android:color/white">

                        <AutoCompleteTextView
                            android:id="@+id/spinner_month"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:inputType="none"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:textColorHint="@android:color/white"
                            android:paddingVertical="16dp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_year"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="Año"
                        android:minHeight="56dp"
                        android:layout_marginStart="12dp"
                        app:boxStrokeColor="@color/white_stroke_color"
                        app:hintTextColor="@color/white_hint_color"
                        app:boxCornerRadiusTopStart="12dp"
                        app:boxCornerRadiusTopEnd="12dp"
                        app:boxCornerRadiusBottomStart="12dp"
                        app:boxCornerRadiusBottomEnd="12dp"
                        app:boxStrokeWidth="2dp"
                        app:boxStrokeWidthFocused="2dp"
                        app:boxBackgroundColor="@android:color/transparent"
                        app:startIconTint="@android:color/white"
                        app:endIconTint="@android:color/white">

                        <AutoCompleteTextView
                            android:id="@+id/spinner_year"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:inputType="none"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:textColorHint="@android:color/white"
                            android:paddingVertical="16dp" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <!-- Selector para Año (solo año) -->
                <LinearLayout
                    android:id="@+id/year_selection_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:visibility="gone">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_year_only"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="Año"
                        android:minHeight="56dp"
                        app:boxStrokeColor="@color/white_stroke_color"
                        app:hintTextColor="@color/white_hint_color"
                        app:boxCornerRadiusTopStart="12dp"
                        app:boxCornerRadiusTopEnd="12dp"
                        app:boxCornerRadiusBottomStart="12dp"
                        app:boxCornerRadiusBottomEnd="12dp"
                        app:boxStrokeWidth="2dp"
                        app:boxStrokeWidthFocused="2dp"
                        app:boxBackgroundColor="@android:color/transparent"
                        app:startIconTint="@android:color/white"
                        app:endIconTint="@android:color/white">

                        <AutoCompleteTextView
                            android:id="@+id/spinner_year_only"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:inputType="none"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:textColor="@android:color/white"
                            android:textColorHint="@android:color/white"
                            android:paddingVertical="16dp" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Dashboard Cards con SwipeRefreshLayout -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/tabs_container"
            app:layout_constraintBottom_toBottomOf="parent">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- ===== TUS CARDS ORIGINALES ===== -->


                <!-- ===== SEPARADOR VISUAL ===== -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/gray"
                    android:alpha="0.3"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 Analytics Dashboard"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:gravity="center"
                    android:layout_marginBottom="24dp" />

                <!-- ===== CONTENIDO DE ANALYTICS ===== -->

                <!-- KPI Cards - adaptadas a tu estilo -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:layout_marginBottom="16dp">

                    <!-- Total Usuarios -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="16dp"
                            android:gravity="center"
                            android:background="@color/primary">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_people_24"
                                android:tint="@color/white"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:id="@+id/tv_total_users"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1,247"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Total Usuarios"
                                android:textSize="11sp"
                                android:textColor="@color/white"
                                android:alpha="0.8" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Tours Activos -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="16dp"
                            android:gravity="center"
                            android:background="@color/green">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_map_location"
                                android:tint="@color/white"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:id="@+id/tv_active_tours"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="23"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tours Activos"
                                android:textSize="11sp"
                                android:textColor="@color/white"
                                android:alpha="0.8" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Ingresos y Reservas -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp"
                    android:paddingHorizontal="16dp">

                    <!-- Ingresos -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="16dp"
                            android:gravity="center"
                            android:background="@color/orange">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_attach_money_24"
                                android:tint="@color/white"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:id="@+id/tv_revenue"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="S/ 45,250"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ingresos"
                                android:textSize="11sp"
                                android:textColor="@color/white"
                                android:alpha="0.8" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Reservas -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:padding="16dp"
                            android:gravity="center"
                            android:background="@color/gray">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_book_24"
                                android:tint="@color/white"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:id="@+id/tv_bookings"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="156"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Reservas"
                                android:textSize="11sp"
                                android:textColor="@color/white"
                                android:alpha="0.8" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Gráfico de Ingresos - CON TU ESTILO DE CARD -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginHorizontal="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">



                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ingresos"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginBottom="8dp" />

                            <com.github.mikephil.charting.charts.LineChart
                                android:id="@+id/line_chart_revenue"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Gráfico de Precio Promedio por Persona -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginHorizontal="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Precio Promedio por Persona"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginBottom="8dp" />

                            <com.github.mikephil.charting.charts.LineChart
                                android:id="@+id/line_chart_average_price"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Gráfico de Barras - Reservas por Mes -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginHorizontal="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">



                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Cantidad de reservas"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginBottom="8dp" />

                            <com.github.mikephil.charting.charts.BarChart
                                android:id="@+id/bar_chart_bookings"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Gráfico de Barras - Personas por Mes -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginHorizontal="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Cantidad de personas"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginBottom="8dp" />

                            <com.github.mikephil.charting.charts.BarChart
                                android:id="@+id/bar_chart_people"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Gráfico de Distribución de Tours -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginHorizontal="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">



                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tours por Categoría"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/black"
                                android:layout_marginBottom="8dp" />

                            <com.github.mikephil.charting.charts.PieChart
                                android:id="@+id/pie_chart_tours"
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            </ScrollView>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        
        <!-- FAB para exportar reportes - POSICIONADO CORRECTAMENTE -->
        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/fab_export"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:text="Exportar"
            android:textColor="@color/white"
            app:icon="@drawable/ic_download_24"
            app:backgroundTint="@color/primary"
            app:iconTint="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer - MANTENGO TU DRAWER ORIGINAL -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="280dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:menu="@menu/superadmin_nav_menu"
        app:headerLayout="@layout/nav_header_superadmin_neotest"
        app:itemIconTint="?attr/colorOnSurfaceVariant"
        app:itemTextColor="?attr/colorOnSurface"
        app:itemShapeInsetStart="16dp"
        app:itemShapeInsetEnd="16dp"
        app:itemVerticalPadding="12dp"
        app:dividerInsetStart="16dp"
        app:dividerInsetEnd="16dp"
        app:itemShapeFillColor="@color/nav_active_indicator"/>



</androidx.drawerlayout.widget.DrawerLayout>