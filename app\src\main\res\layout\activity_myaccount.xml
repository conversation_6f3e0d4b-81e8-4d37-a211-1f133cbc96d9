<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="@string/mi_cuenta"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleCentered="true"
        app:titleTextColor="@color/white"
        app:navigationIcon="?attr/homeAsUpIndicator"
        app:navigationIconTint="@color/white"/>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/profile_header_bg"
        android:padding="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingStart="20dp"
            android:paddingEnd="10dp">


            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:layout_marginVertical="15dp">

                <View
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:layout_gravity="center"
                    android:background="@drawable/profile_pink_circle" />

                <ImageView
                    android:id="@+id/profile_image"
                    android:layout_width="95dp"
                    android:layout_height="95dp"
                    android:layout_gravity="center"
                    android:background="@drawable/profile_image_circle"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_avatar_24" />
            </FrameLayout>


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="María González"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:textColor="#FFFFFF"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_user_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="14sp"
                    android:textColor="#E0E0E0"
                    android:maxLines="1"
                    android:ellipsize="end" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">


            <androidx.cardview.widget.CardView
                android:id="@+id/card_my_profile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp">

                        <View
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/icon_background_blue" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_person"
                            android:tint="#2196F3" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mi Perfil"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ver y editar información personal"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right"
                        android:tint="#BDBDBD" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Nuevo CardView para Perfil de Empresa (inicialmente oculto) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_business_profile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:layout_marginHorizontal="2dp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp">

                        <View
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/icon_background_orange" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_company"
                            android:tint="#FF9800" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Perfil de Empresa"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Editar o cambiar datos empresariales"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right"
                        android:tint="#BDBDBD" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_payment_methods"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:layout_marginHorizontal="2dp"
                >

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp">

                        <View
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/icon_background_green" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_payment"
                            android:tint="#4CAF50" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Métodos de Pago"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Gestionar tarjetas y formas de pago"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right"
                        android:tint="#BDBDBD" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <androidx.cardview.widget.CardView
                android:id="@+id/card_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp">

                        <View
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/icon_background_purple" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_settings"
                            android:tint="#9C27B0" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Preferencias"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Configuración y ajustes de la app"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right"
                        android:tint="#BDBDBD" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_logout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:foreground="?attr/selectableItemBackground"
                android:clickable="true"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp">

                        <View
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/icon_background_red" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_logout"
                            android:tint="#F44336" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Cerrar Sesión"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#F44336" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Salir de tu cuenta"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_chevron_right"
                        android:tint="#BDBDBD" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>

</LinearLayout>