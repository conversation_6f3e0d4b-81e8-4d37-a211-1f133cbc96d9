<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Gestión de Paradas"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleCentered="true"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_back"
        app:navigationIconTint="@color/white"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Map inside a Card (rounded like your other screens) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_map_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:cardCornerRadius="0dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/scroll_container"
        app:layout_constraintHeight_percent="0.40">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Google Map -->
            <fragment
                android:id="@+id/map_fragment"
                android:name="com.google.android.gms.maps.SupportMapFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <!-- Small gradient overlay to improve readability (optional, lightweight) -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#0A000000" />

        </FrameLayout>

    </com.google.android.material.card.MaterialCardView>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/card_map_container"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:paddingTop="13dp">

            <!-- Tour Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#ECECEC"
                android:layout_marginBottom="14dp"
                android:layout_marginHorizontal="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <FrameLayout
                            android:layout_width="42dp"
                            android:layout_height="42dp"
                            android:layout_marginEnd="12dp">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="42dp"
                                android:layout_height="42dp"
                                app:cardCornerRadius="21dp"
                                app:cardElevation="0dp"
                                app:cardBackgroundColor="#1A2196F3" />

                            <ImageView
                                android:layout_width="22dp"
                                android:layout_height="22dp"
                                android:layout_gravity="center"
                                android:src="@android:drawable/ic_menu_mapmode"
                                android:tint="@color/primary" />
                        </FrameLayout>

                        <TextView
                            android:id="@+id/tv_tour_name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="City Tour Lima"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#202124"
                            android:maxLines="1"
                            android:ellipsize="end" />

                    </LinearLayout>

                    <!-- Stats row -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:baselineAligned="false">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="10dp">

                            <TextView
                                android:id="@+id/tv_total_stops"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="5"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Total Paradas"
                                android:textSize="12sp"
                                android:textColor="#6B7280"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="#ECECEC" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="10dp">

                            <TextView
                                android:id="@+id/tv_completed_stops"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="2"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/green" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Confirmadas"
                                android:textSize="12sp"
                                android:textColor="#6B7280"
                                android:layout_marginTop="4dp" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Meeting Point Card (cleaner) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_meeting_point"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#ECECEC"
                android:layout_marginBottom="14dp"
                android:layout_marginHorizontal="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <FrameLayout
                            android:layout_width="38dp"
                            android:layout_height="38dp"
                            android:layout_marginEnd="10dp">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="38dp"
                                android:layout_height="38dp"
                                app:cardCornerRadius="19dp"
                                app:cardElevation="0dp"
                                app:cardBackgroundColor="#1A4CAF50" />

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_gravity="center"
                                android:src="@android:drawable/ic_dialog_map"
                                android:tint="@color/green" />
                        </FrameLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Punto de Encuentro"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#202124" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_meeting_point_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Plaza Mayor de Lima"
                        android:textSize="14sp"
                        android:textColor="#6B7280"
                        android:layout_marginTop="10dp"
                        android:layout_marginStart="48dp"
                        android:maxLines="2"
                        android:ellipsize="end" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_confirm_meeting_point"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Confirmar Llegada"
                        android:textAllCaps="false"
                        android:layout_marginTop="12dp"
                        app:cornerRadius="12dp"
                        app:icon="@drawable/ic_check"
                        app:iconGravity="textStart"
                        app:iconPadding="10dp"
                        />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Stops List Header -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Paradas del Tour"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#202124"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="10dp"
                android:layout_marginHorizontal="13dp"/>

            <!-- Stops RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_stops"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                android:paddingBottom="6dp"
                android:clipToPadding="false" />

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
