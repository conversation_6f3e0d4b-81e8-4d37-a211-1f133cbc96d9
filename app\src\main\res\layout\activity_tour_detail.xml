<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- App Bar Layout con imagen colapsable -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:theme="@style/ThemeOverlay.Material3.Dark">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="320dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed|snap"
            app:contentScrim="@color/primary"
            app:statusBarScrim="@color/primary"
            app:expandedTitleMarginStart="20dp"
            app:expandedTitleMarginBottom="20dp"
            app:expandedTitleTextAppearance="@style/TextAppearance.Material3.HeadlineMedium"
            app:collapsedTitleTextAppearance="@style/TextAppearance.Material3.TitleMedium">

            <!-- Imagen del Tour -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_collapseMode="parallax"
                app:layout_collapseParallaxMultiplier="0.7">

                <ImageView
                    android:id="@+id/iv_header_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:background="#E0E0E0"
                    android:contentDescription="Imagen del tour"
                    tools:src="@drawable/sample_tour" />

                <!-- Gradient overlay para mejor legibilidad -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/gradient_overlay" />

                <!-- Badges flotantes -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="top"
                    android:layout_marginTop="70dp"
                    android:paddingHorizontal="16dp"
                    android:gravity="end">

                </LinearLayout>
            </FrameLayout>

            <!-- Toolbar -->
            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:elevation="0dp"
                app:layout_collapseMode="pin"
                app:navigationIcon="?attr/homeAsUpIndicator"
                app:navigationIconTint="@color/white" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Contenido principal con scroll -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="16dp"
            android:paddingTop="20dp"
            android:paddingBottom="100dp">

            <!-- Header: Título, Rating y Compañía -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Título del tour -->
                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="City Tour Lima Centro Histórico"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C"
                        android:lineSpacingExtra="4dp"
                        tools:text="City Tour Lima Centro Histórico" />

                    <!-- Compañía -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginTop="8dp">

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_company"
                            android:tint="@color/primary"
                            android:layout_marginEnd="6dp"
                            android:contentDescription="Empresa" />

                        <TextView
                            android:id="@+id/tv_company_name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Lima Adventure Tours"
                            android:textSize="14sp"
                            android:textColor="@color/primary"
                            android:textStyle="bold"
                            tools:text="Lima Adventure Tours" />

                        <ImageButton
                            android:id="@+id/btn_company_profile"
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:src="@drawable/ic_chevron_right"
                            android:tint="@color/primary"
                            android:contentDescription="Ver perfil" />
                    </LinearLayout>

                    <!-- Divisor -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="16dp" />

                    <!-- Rating y Reviews -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_star_filled"
                            android:tint="#FF9800"
                            android:layout_marginEnd="6dp"
                            android:contentDescription="Rating" />

                        <TextView
                            android:id="@+id/tv_rating"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.9"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C"
                            tools:text="4.9" />

                        <TextView
                            android:id="@+id/tv_reviews_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" (127 reseñas)"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            tools:text=" (127 reseñas)" />
                    </LinearLayout>

                    <!-- Divisor -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginVertical="16dp" />

                    <!-- Descripción -->
                    <TextView
                        android:id="@+id/tv_tour_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Descubre la historia y cultura de Lima visitando sus principales atractivos del centro histórico. Un recorrido completo por la Plaza de Armas, Catedral, Palacio de Gobierno y los balcones coloniales más emblemáticos."
                        android:textSize="15sp"
                        android:textColor="#424242"
                        android:lineSpacingExtra="4dp"
                        tools:text="Descubre la historia y cultura de Lima visitando sus principales atractivos del centro histórico." />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Detalles Rápidos -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_info"
                            android:tint="@color/primary"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Información" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Detalles del Tour"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="16dp" />

                    <!-- Grid de detalles -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:baselineAligned="false">

                        <!-- Duración -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp"
                            android:background="@drawable/bg_detail_item">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_time"
                                android:tint="#2196F3"
                                android:contentDescription="Duración" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Duración"
                                android:textSize="11sp"
                                android:textColor="#757575"
                                android:layout_marginTop="8dp" />

                            <TextView
                                android:id="@+id/tv_duration"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="4 horas"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C"
                                android:layout_marginTop="4dp"
                                tools:text="4 horas" />
                        </LinearLayout>

                        <!-- Grupo Máximo -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp"
                            android:layout_marginHorizontal="8dp"
                            android:background="@drawable/bg_detail_item">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_people_24"
                                android:tint="#4CAF50"
                                android:contentDescription="Grupo" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Grupo Max"
                                android:textSize="11sp"
                                android:textColor="#757575"
                                android:layout_marginTop="8dp" />

                            <TextView
                                android:id="@+id/tv_group_size"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="12"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C"
                                android:layout_marginTop="4dp"
                                tools:text="12" />
                        </LinearLayout>

                        <!-- Idiomas -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp"
                            android:background="@drawable/bg_detail_item">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_language"
                                android:tint="#FF9800"
                                android:contentDescription="Idiomas" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Idiomas"
                                android:textSize="11sp"
                                android:textColor="#757575"
                                android:layout_marginTop="8dp" />

                            <TextView
                                android:id="@+id/tv_languages"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="ES, EN"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C"
                                android:layout_marginTop="4dp"
                                tools:text="ES, EN" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Servicios Incluidos -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#4CAF50"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Incluido" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Servicios Incluidos"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="16dp" />

                    <!-- Lista de servicios (cargados dinámicamente) -->
                    <LinearLayout
                        android:id="@+id/layout_services"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Itinerario -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_route"
                            android:tint="#2196F3"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Itinerario" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Itinerario"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_view_stops_map"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ver mapa"
                            android:textSize="12sp"
                            app:icon="@drawable/ic_map_location"
                            app:iconSize="18dp" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="16dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_itinerary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="4"
                        tools:listitem="@layout/item_itinerary_point" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Ubicación -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_location"
                            android:tint="#E91E63"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Ubicación" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Punto de Encuentro"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="16dp" />

                    <!-- Mapa -->
                    <FrameLayout
                        android:id="@+id/map_container"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="16dp">

                        <fragment
                            android:id="@+id/meeting_point_map"
                            android:name="com.google.android.gms.maps.SupportMapFragment"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />
                    </FrameLayout>

                    <!-- Dirección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="12dp"
                        android:background="@drawable/bg_detail_item"
                        android:layout_marginBottom="8dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_location"
                            android:tint="#757575"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Dirección" />

                        <TextView
                            android:id="@+id/tv_meeting_point"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Plaza Mayor de Lima, Lima 15001"
                            android:textSize="14sp"
                            android:textColor="#424242"
                            tools:text="Plaza Mayor de Lima, Lima 15001" />
                    </LinearLayout>

                    <!-- Hora de Encuentro -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="12dp"
                        android:background="@drawable/bg_detail_item"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@android:drawable/ic_menu_recent_history"
                            android:tint="#757575"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Hora de encuentro" />

                        <TextView
                            android:id="@+id/tv_meeting_time"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="No especificado"
                            android:textSize="14sp"
                            android:textColor="#424242"
                            tools:text="08:00 AM" />
                    </LinearLayout>

                    <!-- Botón Ver mapa -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_view_map"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:text="Ver mapa"
                        android:textSize="13sp"
                        app:icon="@drawable/ic_map_location"
                        app:iconSize="18dp"
                        app:cornerRadius="8dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Reseñas -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_star_filled"
                            android:tint="#FF9800"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Reseñas" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Reseñas"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_see_all_reviews"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Ver todas"
                            android:textSize="12sp"
                            app:icon="@drawable/ic_chevron_right"
                            app:iconSize="18dp" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginBottom="16dp" />

                    <!-- Resumen de ratings -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@drawable/bg_rating_summary"
                        android:layout_marginBottom="16dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:layout_marginEnd="24dp">

                            <TextView
                                android:id="@+id/tv_average_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="4.9"
                                android:textSize="42sp"
                                android:textStyle="bold"
                                android:textColor="#FF9800"
                                tools:text="4.9" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="14dp"
                                    android:layout_height="14dp"
                                    android:src="@drawable/ic_star_filled"
                                    android:tint="#FF9800"
                                    android:contentDescription="Star" />

                                <ImageView
                                    android:layout_width="14dp"
                                    android:layout_height="14dp"
                                    android:src="@drawable/ic_star_filled"
                                    android:tint="#FF9800"
                                    android:contentDescription="Star" />

                                <ImageView
                                    android:layout_width="14dp"
                                    android:layout_height="14dp"
                                    android:src="@drawable/ic_star_filled"
                                    android:tint="#FF9800"
                                    android:contentDescription="Star" />

                                <ImageView
                                    android:layout_width="14dp"
                                    android:layout_height="14dp"
                                    android:src="@drawable/ic_star_filled"
                                    android:tint="#FF9800"
                                    android:contentDescription="Star" />

                                <ImageView
                                    android:layout_width="14dp"
                                    android:layout_height="14dp"
                                    android:src="@drawable/ic_star_filled"
                                    android:tint="#FF9800"
                                    android:contentDescription="Star" />
                            </LinearLayout>

                            <TextView
                                android:id="@+id/tv_total_reviews"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="127 reseñas"
                                android:textSize="12sp"
                                android:textColor="#757575"
                                android:layout_marginTop="4dp"
                                tools:text="127 reseñas" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- Barra 5 estrellas -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="6dp">

                                <TextView
                                    android:layout_width="16dp"
                                    android:layout_height="wrap_content"
                                    android:text="5"
                                    android:textSize="11sp"
                                    android:textColor="#757575" />

                                <ProgressBar
                                    android:layout_width="0dp"
                                    android:layout_height="8dp"
                                    android:layout_weight="1"
                                    android:layout_marginHorizontal="8dp"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:progress="85"
                                    android:progressTint="#FF9800" />

                                <TextView
                                    android:layout_width="30dp"
                                    android:layout_height="wrap_content"
                                    android:text="85%"
                                    android:textSize="11sp"
                                    android:textColor="#757575"
                                    android:textAlignment="textEnd" />
                            </LinearLayout>

                            <!-- Barra 4 estrellas -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="6dp">

                                <TextView
                                    android:layout_width="16dp"
                                    android:layout_height="wrap_content"
                                    android:text="4"
                                    android:textSize="11sp"
                                    android:textColor="#757575" />

                                <ProgressBar
                                    android:layout_width="0dp"
                                    android:layout_height="8dp"
                                    android:layout_weight="1"
                                    android:layout_marginHorizontal="8dp"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:progress="10"
                                    android:progressTint="#FF9800" />

                                <TextView
                                    android:layout_width="30dp"
                                    android:layout_height="wrap_content"
                                    android:text="10%"
                                    android:textSize="11sp"
                                    android:textColor="#757575"
                                    android:textAlignment="textEnd" />
                            </LinearLayout>

                            <!-- Barra 3 estrellas -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="6dp">

                                <TextView
                                    android:layout_width="16dp"
                                    android:layout_height="wrap_content"
                                    android:text="3"
                                    android:textSize="11sp"
                                    android:textColor="#757575" />

                                <ProgressBar
                                    android:layout_width="0dp"
                                    android:layout_height="8dp"
                                    android:layout_weight="1"
                                    android:layout_marginHorizontal="8dp"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:progress="3"
                                    android:progressTint="#FF9800" />

                                <TextView
                                    android:layout_width="30dp"
                                    android:layout_height="wrap_content"
                                    android:text="3%"
                                    android:textSize="11sp"
                                    android:textColor="#757575"
                                    android:textAlignment="textEnd" />
                            </LinearLayout>

                            <!-- Barra 2 estrellas -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginBottom="6dp">

                                <TextView
                                    android:layout_width="16dp"
                                    android:layout_height="wrap_content"
                                    android:text="2"
                                    android:textSize="11sp"
                                    android:textColor="#757575" />

                                <ProgressBar
                                    android:layout_width="0dp"
                                    android:layout_height="8dp"
                                    android:layout_weight="1"
                                    android:layout_marginHorizontal="8dp"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:progress="1"
                                    android:progressTint="#FF9800" />

                                <TextView
                                    android:layout_width="30dp"
                                    android:layout_height="wrap_content"
                                    android:text="1%"
                                    android:textSize="11sp"
                                    android:textColor="#757575"
                                    android:textAlignment="textEnd" />
                            </LinearLayout>

                            <!-- Barra 1 estrella -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <TextView
                                    android:layout_width="16dp"
                                    android:layout_height="wrap_content"
                                    android:text="1"
                                    android:textSize="11sp"
                                    android:textColor="#757575" />

                                <ProgressBar
                                    android:layout_width="0dp"
                                    android:layout_height="8dp"
                                    android:layout_weight="1"
                                    android:layout_marginHorizontal="8dp"
                                    style="?android:attr/progressBarStyleHorizontal"
                                    android:progress="1"
                                    android:progressTint="#FF9800" />

                                <TextView
                                    android:layout_width="30dp"
                                    android:layout_height="wrap_content"
                                    android:text="1%"
                                    android:textSize="11sp"
                                    android:textColor="#757575"
                                    android:textAlignment="textEnd" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Lista de reseñas -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_reviews"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_review" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Bar fijo -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/bottom_bar_reservation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:cardCornerRadius="0dp"
        app:cardElevation="8dp"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:background="#FFFFFF">

            <!-- Precio -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Precio"
                    android:textSize="11sp"
                    android:textColor="#757575" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:baselineAligned="false"
                    android:layout_marginTop="2dp">

                    <TextView
                        android:id="@+id/tv_price_bottom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 85"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="#4CAF50"
                        tools:text="S/. 85" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="/persona"
                        android:textSize="12sp"
                        android:textColor="#757575"
                        android:layout_marginStart="4dp"
                        android:layout_gravity="bottom"
                        android:layout_marginBottom="3dp" />
                </LinearLayout>
            </LinearLayout>

            <!-- Botones de acción -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_contact"
                    style="@style/Widget.Material3.Button.OutlinedButton.Icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:insetLeft="0dp"
                    android:insetTop="0dp"
                    android:insetRight="0dp"
                    android:insetBottom="0dp"
                    android:layout_marginEnd="12dp"
                    app:icon="@drawable/ic_message"
                    app:iconSize="20dp"
                    app:iconGravity="textStart"
                    app:iconPadding="0dp"
                    app:cornerRadius="12dp"
                    app:strokeColor="@color/primary"
                    android:contentDescription="Contactar" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_book_now"
                    style="@style/Widget.Material3.Button"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:text="Reservar"
                    android:textSize="15sp"
                    android:paddingHorizontal="32dp"
                    app:icon="@drawable/ic_calendar_form"
                    app:iconSize="20dp"
                    app:cornerRadius="12dp"
                    app:backgroundTint="@color/primary" />
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>