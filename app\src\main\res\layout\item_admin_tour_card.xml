<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    android:layout_marginHorizontal="6dp"
    app:cardCornerRadius="14dp"
    app:cardElevation="3dp"
    app:strokeColor="#E0E0E0"
    app:strokeWidth="1dp"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:orientation="vertical"
        android:background="@color/white">

        <!-- HEADER: Imagen + título + estado -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Imagen del tour -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="64dp"
                android:layout_height="64dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="#E0E0E0">

                <ImageView
                    android:id="@+id/iv_tour_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:background="#E0E0E0" />
            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="14dp">

                <TextView
                    android:id="@+id/tv_tour_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Machu Picchu Express"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C"
                    android:maxLines="1"
                    android:ellipsize="end"/>

                <TextView
                    android:id="@+id/tv_tour_dates"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15 - 18 Dic 2024"
                    android:textSize="13sp"
                    android:textColor="#757575"
                    android:layout_marginTop="4dp"/>
            </LinearLayout>

            <!-- Estado -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Activo"
                android:textSize="12sp"
                app:chipCornerRadius="8dp"
                app:chipMinHeight="30dp"
                app:chipBackgroundColor="#E8F5E9"
                android:textColor="#4CAF50"
                app:chipStrokeWidth="0dp"/>
            
            <!-- Botones de acción -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="8dp">
                
                <ImageButton
                    android:id="@+id/btn_edit_tour"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_edit"
                    android:contentDescription="Editar"
                    app:tint="@color/primary"
                    android:padding="6dp"/>
                
                <ImageButton
                    android:id="@+id/btn_delete_tour"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_delete"
                    android:contentDescription="Eliminar"
                    app:tint="#F44336"
                    android:padding="6dp"
                    android:layout_marginStart="4dp"/>
            </LinearLayout>
        </LinearLayout>

        <!-- DIVIDER -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginTop="14dp"
            android:layout_marginBottom="12dp"/>

        <!-- INFO EXTRA -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Categoría/Departamento -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <FrameLayout
                    android:layout_width="28dp"
                    android:layout_height="28dp">

                    <View
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:background="@drawable/icon_background_blue"/>

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_location"
                        android:tint="#2196F3" />
                </FrameLayout>

                <TextView
                    android:id="@+id/tv_guide_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Cusco"
                    android:textSize="13sp"
                    android:textColor="#2196F3"
                    android:maxLines="1"
                    android:ellipsize="end"/>
            </LinearLayout>

            <!-- Precio -->
            <TextView
                android:id="@+id/tv_tour_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/ 850"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#2C2C2C"/>
        </LinearLayout>

        <!-- Chips -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:chipSpacingHorizontal="8dp"
            android:layout_marginTop="10dp">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_duration"
                android:text="3 días"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                app:chipBackgroundColor="#F5F5F5"
                android:textColor="#616161"
                app:chipIcon="@drawable/ic_time"
                app:chipIconSize="14dp"
                app:chipIconTint="#616161"
                app:chipStrokeWidth="0dp" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_bookings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="15/20"
                android:textSize="12sp"
                app:chipBackgroundColor="#F5F5F5"
                app:chipIcon="@drawable/ic_people_24"
                app:chipIconTint="#616161"
                android:textColor="#616161" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_places"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5 lugares"
                android:textSize="12sp"
                app:chipBackgroundColor="#F5F5F5"
                app:chipIcon="@drawable/ic_location"
                app:chipIconTint="#616161"
                android:textColor="#616161" />
        </com.google.android.material.chip.ChipGroup>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
