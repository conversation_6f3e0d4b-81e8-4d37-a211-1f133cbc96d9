<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/white"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Service Image -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="80dp"
            android:layout_height="80dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/iv_service_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/light_gray"
                android:src="@drawable/ic_image" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Service Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp">

            <TextView
                android:id="@+id/tv_service_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Nombre del Servicio"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tv_service_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Descripción del servicio..."
                android:textSize="13sp"
                android:textColor="@color/gray"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/tv_service_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 0.00"
                android:textSize="15sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginTop="6dp" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <ImageButton
                android:id="@+id/btn_edit"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_edit"
                app:tint="@color/primary"
                android:contentDescription="Editar" />

            <ImageButton
                android:id="@+id/btn_delete"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_delete"
                app:tint="#E53935"
                android:contentDescription="Eliminar" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

