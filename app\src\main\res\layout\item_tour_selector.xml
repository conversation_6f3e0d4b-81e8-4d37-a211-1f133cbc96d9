<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_tour"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="12dp"
    app:strokeWidth="2dp"
    app:strokeColor="@color/divider"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Contenido del tour -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Nombre del tour -->
            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Nombre del Tour"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="2"
                android:ellipsize="end" />

            <!-- Fecha y hora -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="6dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_calendar"
                    android:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/tv_tour_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15/12/2025"
                    android:textSize="13sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="4dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_time"
                    android:tint="@color/text_secondary"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/tv_tour_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="09:00"
                    android:textSize="13sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

            <!-- Duración -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_duration"
                    android:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/tv_tour_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4 horas"
                    android:textSize="13sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Indicador de selección -->
        <ImageView
            android:id="@+id/iv_check"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_check_circle"
            android:tint="@color/primary"
            android:visibility="gone"
            android:layout_marginStart="12dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
