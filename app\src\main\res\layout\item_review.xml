<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="top">

    <!-- User Avatar -->
    <LinearLayout
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/circle_primary"
        android:gravity="center"
        android:layout_marginEnd="12dp">

        <TextView
            android:id="@+id/tv_user_initial"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="A"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- Review Content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- User Info and Rating -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="4dp">

            <TextView
                android:id="@+id/tv_user_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Ana García"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_rating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⭐⭐⭐⭐⭐"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Review Text -->
        <TextView
            android:id="@+id/tv_review_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Excelente tour, el guía muy conocedor y amable. Los lugares visitados fueron increíbles y la comida deliciosa."
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_marginBottom="4dp"
            android:lineSpacingExtra="2dp" />

        <!-- Review Date -->
        <TextView
            android:id="@+id/tv_review_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hace 2 semanas"
            android:textSize="12sp"
            android:textColor="@color/gray" />

    </LinearLayout>

</LinearLayout>

