<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Códigos QR"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Scrollable Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Tour Info Header -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_tour_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_map_location"
                        android:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_tour_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="City Tour Lima Centro"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:id="@+id/tv_tour_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="15 Dic, 2024 • 09:00 AM"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_reservation_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="#DT2024001"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/white"
                        android:visibility="gone" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Check-in QR Code -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_checkin_qr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_send"
                            android:tint="@color/green" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Código QR Check-in"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_marginStart="12dp" />

                        <TextView
                            android:id="@+id/tv_checkin_status"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⏳ Pendiente"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            android:background="@drawable/bg_status_pending"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- QR Code Checkin -->
                    <ImageView
                        android:id="@+id/iv_qr_checkin"
                        android:layout_width="250dp"
                        android:layout_height="250dp"
                        android:layout_marginBottom="16dp"
                        android:scaleType="fitXY"
                        android:contentDescription="QR Code Check-in" />

                    <!-- Instructions -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Muestra este código al guía al inicio del tour"
                        android:textSize="14sp"
                        android:textColor="@color/gray"
                        android:textAlignment="center"
                        android:layout_marginBottom="16dp" />

                    <!-- Action Buttons -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_share_checkin"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Compartir"
                            android:drawableStart="@android:drawable/ic_menu_share"
                            android:layout_marginEnd="8dp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_save_checkin"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Guardar"
                            android:drawableStart="@android:drawable/ic_menu_save" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Check-out QR Code -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_checkout_qr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_revert"
                            android:tint="@color/orange" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Código QR Check-out"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_marginStart="12dp" />

                        <TextView
                            android:id="@+id/tv_checkout_status"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="PENDIENTE"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            android:background="@drawable/bg_status_pending"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- QR Code Checkout -->
                    <ImageView
                        android:id="@+id/iv_qr_checkout"
                        android:layout_width="250dp"
                        android:layout_height="250dp"
                        android:layout_marginBottom="16dp"
                        android:scaleType="fitXY"
                        android:contentDescription="QR Code Check-out" />

                    <!-- Instructions -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Muestra este código al guía al finalizar el tour"
                        android:textSize="14sp"
                        android:textColor="@color/gray"
                        android:textAlignment="center"
                        android:layout_marginBottom="16dp" />

                    <!-- Action Buttons -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_share_checkout"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Compartir"
                            android:drawableStart="@android:drawable/ic_menu_share"
                            android:layout_marginEnd="8dp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_save_checkout"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Guardar"
                            android:drawableStart="@android:drawable/ic_menu_save" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Instructions Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/light_gray">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋 Instrucciones"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="1. Muestra el QR de Check-in al guía\n2. Al finalizar, muestra el QR de Check-out\n3. Después podrás calificar tu experiencia"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
