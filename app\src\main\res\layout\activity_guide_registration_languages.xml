<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#825252">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:id="@+id/tvRegresar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Regresar"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:clickable="true"
            android:focusable="true"
            android:padding="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="16dp">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_mountain"
                android:tint="#FFFFFF"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Registro Guía de Turismo"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginStart="12dp"/>
        </LinearLayout>
    </LinearLayout>

    <!-- Contenedor blanco con esquinas redondeadas -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardElevation="0dp"
        app:cardCornerRadius="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Título -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Selecciona los idiomas que domina"
                android:textColor="#666666"
                android:textSize="16sp"
                android:layout_marginBottom="16dp"/>

            <!-- Buscador de idiomas -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.Material3.TextInputLayout.FilledBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Busca un idioma"
                app:boxBackgroundColor="#F8F8F8"
                app:boxCornerRadiusBottomEnd="8dp"
                app:boxCornerRadiusBottomStart="8dp"
                app:boxCornerRadiusTopEnd="8dp"
                app:boxCornerRadiusTopStart="8dp"
                app:startIconDrawable="@android:drawable/ic_menu_search"
                app:startIconTint="#8E8E93"
                app:hintTextColor="#8E8E93">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etBuscarIdioma"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:textColor="#000000"
                    android:textSize="16sp"/>
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Contenedor de Chips Seleccionados -->
            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chipGroupSelectedLanguages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:chipSpacing="8dp"
                app:singleLine="false"/>

            <!-- Divisor -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0"
                android:layout_marginBottom="16dp"/>

            <!-- Lista de idiomas disponibles -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvLanguages"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="vertical"
                android:clipToPadding="false"
                android:paddingBottom="8dp"/>

            <!-- Botón Siguiente -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSiguiente"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Siguiente"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:layout_marginTop="16dp"
                app:cornerRadius="28dp"
                app:icon="@android:drawable/ic_media_play"
                app:iconGravity="end"
                app:iconTint="#FFFFFF"
                android:backgroundTint="#2196F3"
                android:enabled="false"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>