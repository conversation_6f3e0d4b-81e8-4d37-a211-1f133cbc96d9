<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical">

    <!-- Stop Number -->
    <TextView
        android:id="@+id/tv_stop_number"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:text="1"
        android:gravity="center"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:background="@drawable/circle_red" />

    <!-- Stop Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="12dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_stop_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Catedral de Lima"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/tv_stop_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:layout_marginTop="4dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Status -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="✓ Completada"
        android:textSize="12sp"
        android:textStyle="bold"
        android:textColor="@android:color/holo_green_dark" />

</LinearLayout>
