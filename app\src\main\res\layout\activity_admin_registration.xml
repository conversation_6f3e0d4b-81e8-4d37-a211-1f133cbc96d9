<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/main"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- AppBarLayout -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Registro de Administrador"
            app:titleTextAppearance="@style/Toolbar.TitleText3"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="120dp">

            <!-- Header informativo -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@color/primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_admin"
                        app:tint="@color/white"
                        android:contentDescription="Registro" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Crear Administrador de Empresa"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Registra nuevos administradores para empresas turísticas"
                            android:textSize="14sp"
                            android:textColor="#E8E8E8"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Sección: Información Legal de la Empresa -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_company"
                            app:tint="#9C27B0"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Empresa" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Información Legal de la Empresa"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Razón Social -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_business_name"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Razón Social *"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_company"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_business_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:textSize="16sp"
                            android:textColor="#000000" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- RUC -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_ruc"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="RUC *"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_document"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:counterEnabled="true"
                        app:counterMaxLength="11"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_ruc"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLength="11"
                            android:textSize="16sp"
                            android:textColor="#000000" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Tipo de Persona Jurídica -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_business_type"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Tipo de Persona Jurídica *"
                        android:layout_marginBottom="16dp"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <AutoCompleteTextView
                            android:id="@+id/act_business_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            tools:text="S.A.C." />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Nombre Comercial -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_commercial_name"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Nombre Comercial"
                        app:startIconDrawable="@drawable/ic_company"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_commercial_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textSize="16sp"
                            android:textColor="#000000" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Información de campo -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="* Campos obligatorios según SUNAT"
                        android:textSize="12sp"
                        android:textColor="#757575"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Datos del Representante Legal -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_person"
                            app:tint="#2196F3"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Representante" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Datos del Representante Legal"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Tipo de Documento + Número -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Tipo Documento -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Documento *"
                            android:layout_marginEnd="8dp"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <AutoCompleteTextView
                                android:id="@+id/act_admin_doc_type"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                tools:text="DNI" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Número Documento -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Número *"
                            android:layout_marginStart="8dp"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_admin_doc_number"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number"
                                android:textSize="16sp"
                                android:textColor="#000000" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- Nombres y Apellidos -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Nombres *"
                            android:layout_marginEnd="8dp"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_admin_first_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textPersonName"
                                android:maxLength="40"
                                android:textSize="16sp"
                                android:textColor="#000000" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Apellidos *"
                            android:layout_marginStart="8dp"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_admin_last_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textPersonName"
                                android:maxLength="40"
                                android:textSize="16sp"
                                android:textColor="#000000" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- Email Corporativo -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_admin_email"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Correo Corporativo *"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_email"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:endIconMode="clear_text"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_admin_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            android:importantForAutofill="no" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Teléfono -->
                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_admin_phone"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Teléfono de Contacto"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_admin_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="phone"
                                android:maxLength="11"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                android:gravity="start|center_vertical"
                                android:paddingStart="110dp"/>
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.hbb20.CountryCodePicker
                            android:id="@+id/ccp_admin"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start|center_vertical"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="8dp"
                            app:ccp_defaultNameCode="PE"
                            app:ccp_showFlag="true"
                            app:ccp_showNameCode="false"
                            app:ccp_showFullName="false"
                            app:ccp_showPhoneCode="true"
                            app:ccp_contentColor="#000000"
                            app:ccp_textSize="16sp"
                            app:ccp_arrowSize="12dp"
                            app:ccp_customMasterCountries="PE,ES,MX,AR,CO,CL,EC,BO,VE,UY,PY,BR" />
                    </FrameLayout>


                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Credenciales de Acceso -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_password"
                            app:tint="#FF9800"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Credenciales" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Credenciales de Acceso"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Contraseña Temporal -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_admin_password"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Contraseña Temporal *"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_password"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:endIconMode="password_toggle"
                        app:passwordToggleEnabled="true"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_admin_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            android:importantForAutofill="no"
                            tools:text="TempPass123!" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Confirmar Contraseña -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_admin_confirm_password"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Confirmar Contraseña *"
                        android:layout_marginBottom="12dp"
                        app:startIconDrawable="@drawable/ic_password"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:endIconMode="password_toggle"
                        app:passwordToggleEnabled="true"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_admin_confirm_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            android:importantForAutofill="no" />
                    </com.google.android.material.textfield.TextInputLayout>




                </LinearLayout>
            </androidx.cardview.widget.CardView>





        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- FAB para registrar -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/btn_register_admin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:text="Registrar"
        app:icon="@drawable/ic_check_circle"
        app:backgroundTint="@color/primary"
        app:iconTint="@color/white"
        android:textColor="@color/white"
        app:elevation="8dp"
        app:extendStrategy="wrap_content" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>