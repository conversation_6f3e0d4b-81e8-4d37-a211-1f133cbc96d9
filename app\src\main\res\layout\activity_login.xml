<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    android:id="@+id/main">

    <!-- Logo fijo en la parte superior -->
    <ImageView
        android:id="@+id/imageninit"
        android:layout_width="456dp"
        android:layout_height="487dp"
        android:layout_gravity="top|center_horizontal"
        android:contentDescription="@string/app_name"
        android:src="@drawable/head" />

    <!-- ScrollView para el contenido principal -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="90dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">

            <!-- Título de la app -->
            <TextView
                android:id="@+id/tv_app_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/bricolage_grotesque_medium"
                android:text="@string/app_name"
                android:textAppearance="@style/droidtour.white"
                app:layout_constraintBottom_toTopOf="@+id/card_login"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginBottom="50dp" />

            <!-- Card de login -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_app_title">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/email"
                        app:startIconDrawable="@drawable/ic_email"
                        app:hintTextColor="@color/primary"
                        app:boxStrokeColor="@color/primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:hint="@string/password"
                        app:endIconMode="password_toggle"
                        app:startIconDrawable="@drawable/ic_password"
                        app:hintTextColor="@color/primary"
                        app:boxStrokeColor="@color/primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:id="@+id/tv_forgot_password"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:text="@string/forgot_password"
                        android:textColor="@color/primary"
                        android:textSize="14sp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_login"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:layout_marginTop="24dp"
                        android:backgroundTint="@color/primary"
                        android:text="@string/login"
                        android:textSize="16sp"
                        app:cornerRadius="8dp" />

                    <!-- Separador O -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1"
                            android:background="@color/dark_gray" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_marginEnd="12dp"
                            android:text="O"
                            android:textColor="@color/dark_gray"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1"
                            android:background="@color/dark_gray" />

                    </LinearLayout>

                    <!-- Botón de Google -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_google_sign_in"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:layout_marginTop="16dp"
                        android:text="Continuar con Google"
                        android:textColor="@color/dark_gray"
                        android:textSize="16sp"
                        app:cornerRadius="8dp"
                        app:icon="@drawable/ic_google"
                        android:drawableTint="@color/dark_gray"
                        app:iconGravity="textStart"
                        app:iconPadding="12dp"
                        app:iconTint="@color/dark_gray"
                        app:strokeColor="@color/dark_gray"
                        app:strokeWidth="1dp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Sección de registro -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/card_login">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¿No tienes cuenta? "
                    android:textColor="@color/dark_gray" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_register"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/register" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</FrameLayout>