<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_user"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="#E0E0E0">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:background="@color/white">

        <!-- Avatar circular con inicial -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_avatar"
            android:layout_width="56dp"
            android:layout_height="56dp"
            app:cardCornerRadius="28dp"
            app:cardElevation="0dp"
            app:strokeWidth="2dp"
            app:strokeColor="@color/primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/primary_light">

                <ImageView
                    android:id="@+id/iv_user_avatar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:contentDescription="Avatar del usuario" />

                <TextView
                    android:id="@+id/tv_avatar_initial"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="JP"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    tools:text="JP" />

            </FrameLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Indicador de estado (punto) - MEJORADO -->
        <View
            android:id="@+id/view_status_indicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/card_avatar"
            app:layout_constraintBottom_toBottomOf="@id/card_avatar" />

        <!-- Chip de estado (texto) para Guía de Turismo - Movido a posición del botón Editar -->

        <!-- Nombre del usuario -->
        <TextView
            android:id="@+id/tv_user_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Juan Pérez González"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#212121"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintStart_toEndOf="@id/card_avatar"
            app:layout_constraintEnd_toStartOf="@id/btn_delete_user"
            app:layout_constraintTop_toTopOf="@id/card_avatar"
            tools:text="Juan Pérez González" />

        <!-- Email del usuario -->
        <TextView
            android:id="@+id/tv_user_email"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="<EMAIL>"
            android:textSize="14sp"
            android:textColor="#757575"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="2dp"
            app:layout_constraintStart_toStartOf="@id/tv_user_name"
            app:layout_constraintEnd_toEndOf="@id/tv_user_name"
            app:layout_constraintTop_toBottomOf="@id/tv_user_name"
            tools:text="<EMAIL>" />


        <!-- Botón eliminar usuario (rojo) -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_delete_user"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:minWidth="0dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="Eliminar"
            android:textSize="13sp"
            android:visibility="gone"
            app:icon="@drawable/ic_trash"
            app:iconTint="@color/red_dark"
            app:iconSize="18dp"
            app:iconGravity="textStart"
            app:cornerRadius="8dp"
            app:strokeColor="@color/red_dark"
            android:textColor="@color/red_dark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="Eliminar usuario" />

        <!-- Divisor -->
        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="#E0E0E0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_user_email" />

        <!-- Tipo de usuario con icono (movido a la posición del chip de fecha) -->
        <LinearLayout
            android:id="@+id/layout_user_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical|start"
            android:layout_marginTop="12dp"
            android:layout_marginStart="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/switch_user_status"
            app:layout_constraintTop_toBottomOf="@id/divider">

            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_person"
                android:tint="#9E9E9E"
                android:contentDescription="Tipo" />

            <TextView
                android:id="@+id/tv_user_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cliente"
                android:textSize="12sp"
                android:textColor="#9E9E9E"
                android:layout_marginStart="4dp"
                tools:text="Cliente" />

        </LinearLayout>

        <!-- Switch de estado activo/inactivo -->
        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switch_user_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Activo"
            android:textSize="12sp"
            android:checked="true"
            app:thumbTint="@color/switch_thumb_color"
            app:trackTint="@color/switch_track_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/layout_user_type"
            app:layout_constraintBottom_toBottomOf="@id/layout_user_type" />

        <!-- Botones de acción rápida -->
        <LinearLayout
            android:id="@+id/layout_actions"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="start"
            android:layout_marginTop="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_user_type">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_user"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="Ver Perfil"
                android:textSize="13sp"
                app:icon="@drawable/ic_profile_details"
                app:iconTint="@color/primary"
                app:iconSize="18dp"
                app:iconGravity="textStart"
                app:cornerRadius="8dp"
                app:strokeColor="@color/primary"
                android:textColor="@color/primary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_user"
                style="@style/Widget.Material3.Button.TonalButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Editar"
                android:textSize="13sp"
                app:icon="@drawable/ic_edit"
                app:iconTint="@color/primary"
                app:iconSize="18dp"
                app:iconGravity="textStart"
                app:cornerRadius="8dp"
                app:backgroundTint="@color/primary_light" />

            <!-- Chip de estado Pendiente (reemplaza botón Editar cuando está pendiente) -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_status"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:visibility="gone"
                android:text="Pendiente"
                android:textSize="13sp"
                android:textStyle="bold"
                android:gravity="center"
                android:textAlignment="center"
                app:chipMinHeight="40dp"
                app:chipStrokeWidth="0dp"
                app:chipBackgroundColor="@color/notification_orange"
                android:textColor="@color/white" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>