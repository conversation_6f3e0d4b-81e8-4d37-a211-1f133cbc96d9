<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- ================= APP BAR ================= -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Chat con Clientes"
            app:titleCentered="true"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways" />
    </com.google.android.material.appbar.AppBarLayout>

    <!-- ================= CONTENT ================= -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <!-- ================= SEARCH ================= -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:layout_marginHorizontal="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Search -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Buscar conversación"
                        app:startIconDrawable="@drawable/ic_search_form"
                        app:boxBackgroundMode="outline"
                        app:boxCornerRadiusTopStart="12dp"
                        app:boxCornerRadiusTopEnd="12dp"
                        app:boxCornerRadiusBottomStart="12dp"
                        app:boxCornerRadiusBottomEnd="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_search_chat"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />
                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- ================= CHAT LIST ================= -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_client_chats"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>
    </ScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
