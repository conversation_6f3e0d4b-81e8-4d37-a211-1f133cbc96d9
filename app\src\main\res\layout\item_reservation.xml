<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="3dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Status Header con gradiente visual -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/green"
            android:padding="16dp"
            android:gravity="center_vertical"
            android:elevation="2dp">

            <androidx.cardview.widget.CardView
                android:layout_width="36dp"
                android:layout_height="36dp"
                app:cardCornerRadius="18dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="#FFFFFF">

                <ImageView
                    android:id="@+id/iv_status_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_calendar_check"
                    android:layout_gravity="center"
                    app:tint="@color/green" />

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_reservation_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="CONFIRMADA"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:textColor="#FFFFFF"
                    android:fontFamily="sans-serif-medium"
                    android:letterSpacing="0.05" />

                <TextView
                    android:id="@+id/tv_reservation_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Código: #DT2024001"
                    android:textSize="12sp"
                    android:textColor="#E8F5E9"
                    android:layout_marginTop="2dp"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="#FFFFFF">

                <TextView
                    android:id="@+id/tv_reservation_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15 Dic"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:textColor="@color/green"
                    android:fontFamily="sans-serif-medium"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp" />

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Reservation Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="18dp"
            android:background="@color/white">

            <!-- Tour Image + Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp">

                    <ImageView
                        android:id="@+id/iv_tour_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@android:drawable/ic_menu_gallery"
                        android:contentDescription="Imagen del tour" />

                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="12dp"
                    android:orientation="vertical"
                    android:layout_gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="City Tour Lima Centro Histórico"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#1A1A1A"
                        android:fontFamily="sans-serif-medium"
                        android:lineSpacingExtra="2dp"
                        android:maxLines="2"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/tv_company_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Lima Adventure Tours"
                        android:textSize="14sp"
                        android:textColor="#4A90E2"
                        android:fontFamily="sans-serif"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Divider sutil -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E0E0E0"
                android:layout_marginBottom="16dp" />

            <!-- Tour Details Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📅 Fecha"
                        android:textSize="12sp"
                        android:textColor="#9E9E9E"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tv_tour_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="15 Dic, 2024"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#424242"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="#E0E0E0"
                    android:layout_marginHorizontal="8dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏰ Hora"
                        android:textSize="12sp"
                        android:textColor="#9E9E9E"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tv_tour_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="09:00 AM"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#424242"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="#E0E0E0"
                    android:layout_marginHorizontal="8dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="👥 Personas"
                        android:textSize="12sp"
                        android:textColor="#9E9E9E"
                        android:fontFamily="sans-serif" />

                    <TextView
                        android:id="@+id/tv_participants"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#424242"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Payment Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="#F5F9FF"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="14dp"
                    android:gravity="center_vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#E8F5E9">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_payment"
                            android:layout_gravity="center"
                            app:tint="#4CAF50" />

                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="12dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Pago procesado"
                            android:textSize="12sp"
                            android:textColor="#757575"
                            android:fontFamily="sans-serif" />

                        <TextView
                            android:id="@+id/tv_payment_method"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Visa ****1234"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#424242"
                            android:fontFamily="sans-serif-medium"
                            android:layout_marginTop="2dp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_total_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/. 170.00"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#4CAF50"
                        android:fontFamily="sans-serif-medium" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- QR Code Section (for active reservations) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/layout_qr_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="#FFF8E1"
                app:strokeWidth="1dp"
                app:strokeColor="#FFD54F"
                android:layout_marginBottom="16dp"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="14dp"
                    android:gravity="center_vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="#FFFFFF">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_qr_code"
                            android:layout_gravity="center"
                            app:tint="#FFA000" />

                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="12dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Códigos QR listos"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="#F57C00"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Check-in y Check-out disponibles"
                            android:textSize="12sp"
                            android:textColor="#757575"
                            android:fontFamily="sans-serif"
                            android:layout_marginTop="2dp" />

                    </LinearLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_view_qr"
                        style="@style/Widget.Material3.Button.TonalButton"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:text="Ver QR"
                        android:textSize="13sp"
                        android:fontFamily="sans-serif-medium"
                        app:cornerRadius="20dp"
                        android:textColor="#F57C00"
                        app:backgroundTint="#FFFFFF" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:id="@+id/layout_reservation_actions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_contact_company"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Contactar"
                    android:textSize="13sp"
                    android:fontFamily="sans-serif-medium"
                    android:drawableStart="@android:drawable/ic_menu_call"
                    android:drawablePadding="6dp"
                    app:iconSize="18dp"
                    android:layout_marginEnd="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_view_details"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ver Detalles"
                    android:textSize="13sp"
                    android:fontFamily="sans-serif-medium"
                    app:cornerRadius="20dp"
                    app:strokeWidth="1.5dp"
                    android:layout_marginEnd="8dp" />

                <!-- Rating Button (for completed tours) -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_rate_tour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Calificar"
                    android:textSize="13sp"
                    android:fontFamily="sans-serif-medium"
                    android:drawableStart="@android:drawable/ic_menu_edit"
                    android:drawablePadding="6dp"
                    app:cornerRadius="20dp"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>