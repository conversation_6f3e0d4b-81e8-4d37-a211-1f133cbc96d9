<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Alertas de Check-out"
        app:titleTextColor="@color/white"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Pending Checkouts Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_pending_checkouts"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@color/orange">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_agenda"
                            android:tint="@color/white" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Check-outs Pendientes"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            android:layout_marginStart="12dp" />

                        <TextView
                            android:id="@+id/tv_pending_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3"
                            android:textSize="14sp"
                            android:textColor="@color/orange"
                            android:background="@drawable/circle_white"
                            android:gravity="center"
                            android:minWidth="24dp"
                            android:minHeight="24dp" />

                    </LinearLayout>

                    <!-- Pending List -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_pending_checkouts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="8dp"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Recent Processed Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_processed_checkouts"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="16dp"
                        android:background="@color/green">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_menu_agenda"
                            android:tint="@color/white" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Procesados Recientemente"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            android:layout_marginStart="12dp" />

                        <TextView
                            android:id="@+id/tv_processed_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="8"
                            android:textSize="14sp"
                            android:textColor="@color/green"
                            android:background="@drawable/circle_white"
                            android:gravity="center"
                            android:minWidth="24dp"
                            android:minHeight="24dp" />

                    </LinearLayout>

                    <!-- Processed List -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_processed_checkouts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="8dp"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Payment Summary Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_payment_summary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Resumen de Pagos Hoy"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Total Procesado:"
                            android:textSize="16sp"
                            android:textColor="@color/black" />

                        <TextView
                            android:id="@+id/tv_total_processed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="S/. 1,250.00"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/green" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Tours Completados:"
                            android:textSize="16sp"
                            android:textColor="@color/black" />

                        <TextView
                            android:id="@+id/tv_tours_completed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="8"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Comisión Plataforma:"
                            android:textSize="16sp"
                            android:textColor="@color/black" />

                        <TextView
                            android:id="@+id/tv_platform_commission"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="S/. 125.00"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/orange" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
