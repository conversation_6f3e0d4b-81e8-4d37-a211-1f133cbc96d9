<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            android:elevation="4dp">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/primary"
                app:title="Restablecer Contraseña"
                app:titleTextAppearance="@style/Toolbar.TitleText2"
                app:titleCentered="true"
                app:titleTextColor="@color/white"
                app:navigationIcon="?attr/homeAsUpIndicator"
                app:navigationIconTint="@color/white" />

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Icono de seguridad -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="24dp"
                android:src="@drawable/ic_password"
                android:tint="@color/primary"
                android:contentDescription="Icono de seguridad" />

            <!-- Título -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Crea tu nueva contraseña"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <!-- Descripción -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Por favor, ingresa una contraseña segura que cumpla con los requisitos."
                android:textSize="14sp"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="24dp" />

            <!-- Requisitos de contraseña -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="#E3F2FD"
                app:cardElevation="0dp"
                app:cardCornerRadius="8dp"
                app:strokeWidth="1dp"
                app:strokeColor="#2196F3">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tu contraseña debe contener:"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#1976D2"
                        android:layout_marginBottom="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="4dp">

                        <ImageView
                            android:id="@+id/iv_check_length"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#9E9E9E"
                            android:layout_marginEnd="8dp"
                            android:contentDescription="Check" />

                        <TextView
                            android:id="@+id/tv_check_length"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mínimo 8 caracteres"
                            android:textSize="13sp"
                            android:textColor="#616161" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="4dp">

                        <ImageView
                            android:id="@+id/iv_check_lowercase"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#9E9E9E"
                            android:layout_marginEnd="8dp"
                            android:contentDescription="Check" />

                        <TextView
                            android:id="@+id/tv_check_lowercase"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1 letra en minúscula"
                            android:textSize="13sp"
                            android:textColor="#616161" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="4dp">

                        <ImageView
                            android:id="@+id/iv_check_uppercase"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#9E9E9E"
                            android:layout_marginEnd="8dp"
                            android:contentDescription="Check" />

                        <TextView
                            android:id="@+id/tv_check_uppercase"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1 letra en mayúscula"
                            android:textSize="13sp"
                            android:textColor="#616161" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="4dp">

                        <ImageView
                            android:id="@+id/iv_check_number"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#9E9E9E"
                            android:layout_marginEnd="8dp"
                            android:contentDescription="Check" />

                        <TextView
                            android:id="@+id/tv_check_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1 número"
                            android:textSize="13sp"
                            android:textColor="#616161" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:id="@+id/iv_check_special"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#9E9E9E"
                            android:layout_marginEnd="8dp"
                            android:contentDescription="Check" />

                        <TextView
                            android:id="@+id/tv_check_special"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1 caracter especial (!@#$%^&amp;*)"
                            android:textSize="13sp"
                            android:textColor="#616161" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Campo nueva contraseña -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_new_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Nueva Contraseña"
                app:startIconDrawable="@drawable/ic_password"
                app:startIconTint="@color/primary"
                app:endIconMode="password_toggle"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_marginBottom="16dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_new_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Campo confirmar contraseña -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_confirm_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Confirmar Contraseña"
                app:startIconDrawable="@drawable/ic_password"
                app:startIconTint="@color/primary"
                app:endIconMode="password_toggle"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_marginBottom="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_confirm_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Mensaje de error (inicialmente oculto) -->
            <TextView
                android:id="@+id/tv_password_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textColor="#D32F2F"
                android:textSize="13sp"
                android:visibility="gone" />

            <!-- Mensaje de éxito (inicialmente oculto) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_success"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:visibility="gone"
                app:cardBackgroundColor="#E8F5E9"
                app:cardElevation="2dp"
                app:cardCornerRadius="8dp"
                app:strokeWidth="1dp"
                app:strokeColor="#4CAF50">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="#2E7D32"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Éxito" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="¡Contraseña restablecida!"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#1B5E20" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Tu contraseña ha sido actualizada exitosamente. Ahora puedes iniciar sesión con tu nueva contraseña."
                        android:textSize="14sp"
                        android:textColor="#2E7D32"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Botón restablecer -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_reset_password"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:text="Restablecer Contraseña"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="8dp"
                app:elevation="2dp"
                android:layout_marginBottom="16dp"
                app:backgroundTint="@color/primary"
                android:textColor="@color/white"
                android:enabled="false" />

            <!-- Botón cancelar -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Cancelar"
                android:textSize="14sp"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                app:strokeColor="@color/primary"
                app:strokeWidth="1dp"
                app:cornerRadius="8dp"
                app:backgroundTint="@color/white"
                android:textColor="@color/primary" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>