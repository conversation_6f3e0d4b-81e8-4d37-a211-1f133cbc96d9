<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="@color/white">

        <!-- Guide Photo -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_guide_photo"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:src="@drawable/ic_avatar_24"
            android:scaleType="centerCrop"
            app:strokeWidth="2dp"
            app:strokeColor="@color/primary_light"
            app:shapeAppearanceOverlay="@style/CircleImageView" />

        <!-- Guide Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_guide_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Carlos Mendoza"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="City Tour Lima Centro"
                android:textSize="13sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- Info Icon -->
        <ImageButton
            android:id="@+id/btn_show_guide_details"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_info"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:tint="@color/primary"
            android:contentDescription="Ver detalles del guía"
            android:layout_marginStart="8dp" />



    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
