<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Chats con Empresas"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Sección de búsqueda debajo del AppBar -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Barra de búsqueda -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_search"
                    android:tint="#757575"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="Buscar" />

                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:hint="Buscar empresas..."
                    android:textColorHint="#9E9E9E"
                    android:textColor="#2C2C2C"
                    android:textSize="14sp"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1" />

                <ImageButton
                    android:id="@+id/btn_clear_search"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_close"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:tint="#757575"
                    android:visibility="gone"
                    android:contentDescription="Limpiar búsqueda" />
            </LinearLayout>

            <!-- Filtros opcionales (puedes agregarlos si necesitas) -->
            <LinearLayout
                android:id="@+id/layout_filters"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="12dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Filtros:"
                    android:textStyle="bold"
                    android:textSize="12sp"
                    android:textColor="#757575"
                    android:layout_marginEnd="8dp" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_unread"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No leídos"
                    android:textSize="12sp"
                    />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Texto "Sin resultados" (inicialmente oculto) -->
            <LinearLayout
                android:id="@+id/layout_no_results"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_search"
                    android:tint="#BDBDBD"
                    android:contentDescription="Sin resultados" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No se encontraron empresas"
                    android:textSize="16sp"
                    android:textColor="#757575"
                    android:layout_marginTop="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Intenta con otro término de búsqueda"
                    android:textSize="14sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="4dp" />
            </LinearLayout>

            <!-- Lista de chats con empresas -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_company_chats"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>