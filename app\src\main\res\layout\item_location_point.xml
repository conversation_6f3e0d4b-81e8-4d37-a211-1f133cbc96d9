<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Status Indicator -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <TextView
                android:id="@+id/tv_point_number"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:text="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/circle_primary"
                android:gravity="center"
                android:layout_marginBottom="4dp" />

            <View
                android:id="@+id/view_status_indicator"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:background="@drawable/circle_green" />

        </LinearLayout>

        <!-- Location Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_location_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Plaza de Armas"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_location_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Centro histórico de Lima"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginTop="2dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/tv_arrival_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⏰ 10:15 AM"
                    android:textSize="12sp"
                    android:textColor="@color/primary" />

                <TextView
                    android:id="@+id/tv_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="• ⏱️ 30 min"
                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Button -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_register_arrival"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Registrar"
                android:textSize="12sp"
                android:visibility="visible" />

            <!-- Status Text (for completed points) -->
            <TextView
                android:id="@+id/tv_status_completed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✓ Completado"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/green"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
