<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="110dp"
    android:background="@color/primary"
    android:orientation="vertical"
    android:padding="15dp"
    android:paddingStart="20dp"
    android:gravity="bottom">

    <!-- Contenedor de información de usuario -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Avatar -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="64dp"
            android:layout_height="64dp"
            app:cardCornerRadius="32dp"
            app:cardElevation="6dp"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/iv_avatar_header"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_avatar_24"
                android:background="?attr/colorSurface"
                android:scaleType="centerCrop" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Información de usuario -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_user_name_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Gabrielle Ivonne"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:src="@drawable/ic_person"
                    android:tint="@color/white"
                    android:layout_marginEnd="6dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Guía de Turismo"
                    android:textColor="@color/white"
                    android:textSize="15sp"
                    android:alpha="0.9" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- Barra de estado elegante -->


</LinearLayout>