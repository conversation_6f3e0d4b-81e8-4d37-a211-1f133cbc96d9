<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Enviar Propuesta"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_guide_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="a Nombre del Guía"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:layout_marginBottom="24dp" />

    <!-- <PERSON><PERSON><PERSON><PERSON> de tour -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Selecciona el tour"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_tours"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/rounded_background_light" />

    <!-- Monto de pago -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Monto a pagar (S/)"
        app:prefixText="S/ "
        app:startIconDrawable="@drawable/ic_money"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Notas adicionales -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Notas adicionales (opcional)"
        app:startIconDrawable="@drawable/ic_notes"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
        android:layout_marginBottom="24dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="3"
            android:gravity="top" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Botones -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cancelar"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Enviar Propuesta"
            app:icon="@drawable/ic_send"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

</LinearLayout>
