<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- AppBarLayout -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Crear Tour"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="100dp">

            <!-- Header informativo -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@color/primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_map_location"
                        app:tint="@color/white"
                        android:contentDescription="Tour" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Crear Nuevo Tour"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Configura una nueva experiencia turística"
                            android:textSize="14sp"
                            android:textColor="#E8E8E8"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Sección: Imágenes del Tour -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_image"
                            app:tint="#FF9800"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Imágenes" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Imágenes del Tour"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <TextView
                        android:id="@+id/tv_images_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Agrega hasta 5 imágenes para mostrar tu tour (0/5)"
                        android:textColor="#757575"
                        android:textSize="14sp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Placeholder cuando no hay imágenes -->
                    <LinearLayout
                        android:id="@+id/placeholder_images"
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:background="@drawable/dashed_border"
                        android:layout_marginBottom="8dp">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:src="@drawable/ic_image"
                            app:tint="#BDBDBD"
                            android:contentDescription="Agregar imágenes" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Toca para agregar imágenes"
                            android:textColor="#9E9E9E"
                            android:textSize="14sp"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                    <!-- RecyclerView horizontal para imágenes -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_tour_images"
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:orientation="horizontal"
                        android:clipToPadding="false"
                        android:paddingStart="4dp"
                        android:paddingEnd="4dp"
                        android:visibility="gone"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_tour_image" />

                    <!-- Botón para agregar más imágenes -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_add_images"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Agregar más imágenes (0/5)"
                        android:textSize="14sp"
                        app:icon="@drawable/ic_add"
                        app:iconTint="@color/primary"
                        android:layout_marginTop="8dp"
                        android:textColor="@color/primary" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Información Básica -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_info"
                            app:tint="#2196F3"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Info básica" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Información Básica"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Nombre del tour -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_tour_name"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Nombre del Tour"
                        android:layout_marginBottom="16dp"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:counterEnabled="true"
                        app:counterMaxLength="100"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_tour_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLength="100"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            tools:text="Tour Cusco Mágico" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Descripción -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_tour_description"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Descripción"
                        android:layout_marginBottom="16dp"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:counterEnabled="true"
                        app:counterMaxLength="500"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_tour_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:lines="4"
                            android:maxLength="500"
                            android:gravity="top|start"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            tools:text="Descubre la magia de Cusco..." />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Categoría -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_tour_category"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Categoría"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_category"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <AutoCompleteTextView
                            android:id="@+id/act_tour_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            tools:text="Aventura" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Grid de Precio y Duración -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- Precio -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_tour_price"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Precio"
                            android:layout_marginEnd="8dp"
                            app:prefixText="S/ "
                            app:startIconTint="@color/primary"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_tour_price"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="numberDecimal"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                tools:text="150.00" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Duración -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_tour_duration"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Duración"
                            android:layout_marginStart="8dp"
                            app:suffixText=" hrs"
                            app:startIconDrawable="@drawable/ic_time"
                            app:startIconTint="@color/primary"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:helperText="Calculada automáticamente"
                            app:helperTextTextColor="#757575"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_tour_duration"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:focusable="false"
                                android:enabled="false"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                tools:text="2.5" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Fecha y Horario del Tour -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_calendar_check"
                            app:tint="#9C27B0"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Fechas" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Fecha y Horario del Tour"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Fecha del Tour -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_tour_date"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Fecha del Tour"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_calendar_form"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_tour_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false"
                            android:clickable="true"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            tools:text="15/12/2024" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Grid de Hora de Inicio y Hora de Fin -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <!-- Hora de Inicio -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_start_time"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Hora de Inicio"
                            android:layout_marginEnd="8dp"
                            app:startIconDrawable="@drawable/ic_time"
                            app:startIconTint="@color/primary"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_start_time"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:focusable="false"
                                android:clickable="true"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                tools:text="09:00" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- Hora de Fin (calculada) -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_end_time"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="Hora de Fin"
                            android:layout_marginStart="8dp"
                            app:startIconDrawable="@drawable/ic_time"
                            app:startIconTint="@color/primary"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:helperText="Calculada automáticamente"
                            app:helperTextTextColor="#757575"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_end_time"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:focusable="false"
                                android:enabled="false"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                tools:text="17:00" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <!-- Capacidad máxima -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_max_capacity"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Capacidad Máxima"
                        app:startIconDrawable="@drawable/ic_people_24"
                        app:startIconTint="@color/primary"
                        app:suffixText=" personas"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_max_capacity"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            tools:text="15" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Ubicaciones del Tour -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_location"
                            app:tint="#4CAF50"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Ubicaciones" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Ubicaciones del Tour"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />

                        <!-- Badge contador -->
                        <TextView
                            android:id="@+id/tv_locations_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 paradas"
                            android:textSize="13sp"
                            android:textColor="#757575"
                            android:background="@drawable/badge_bg"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="4dp"
                            tools:text="3 paradas" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Agrega las ubicaciones que visitarás en orden"
                        android:textColor="#757575"
                        android:textSize="14sp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Botón agregar ubicación -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_add_location"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:text="Agregar Ubicación en Mapa"
                        android:textSize="15sp"
                        app:icon="@drawable/ic_location_add"
                        app:iconTint="@color/primary"
                        app:iconSize="20dp"
                        app:iconGravity="textStart"
                        app:strokeColor="@color/primary"
                        android:textColor="@color/primary"
                        app:cornerRadius="8dp"
                        android:layout_marginBottom="16dp" />

                    <!-- Lista de ubicaciones -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_locations"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="2"
                        tools:listitem="@layout/item_tour_location" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Servicios Incluidos -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_services"
                            app:tint="#E91E63"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Servicios" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Servicios Incluidos"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Lista dinámica de servicios de la empresa -->
                    <LinearLayout
                        android:id="@+id/tv_no_services"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="24dp"
                        android:visibility="gone">
                        
                        <ImageView
                            android:layout_width="80dp"
                            android:layout_height="80dp"
                            android:src="@drawable/ic_service"
                            android:alpha="0.3"
                            app:tint="#9E9E9E" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="No hay servicios registrados"
                            android:textColor="#424242"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginTop="12dp" />
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Crea tu primer servicio desde Servicios"
                            android:textColor="#757575"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp"
                            android:gravity="center" />
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_services"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_service_checkbox" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>


            <!-- Sección: Información de Encuentro -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_meeting_point"
                            app:tint="#795548"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Punto de encuentro" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Información de Encuentro"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Punto de encuentro -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Selecciona el punto de encuentro en el mapa"
                        android:textColor="#757575"
                        android:textSize="14sp"
                        android:layout_marginBottom="12dp"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_select_meeting_point"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:text="Seleccionar Punto de Encuentro"
                        android:textSize="15sp"
                        app:icon="@drawable/ic_location"
                        app:iconTint="@color/primary"
                        app:iconSize="20dp"
                        app:iconGravity="textStart"
                        app:strokeColor="@color/primary"
                        android:textColor="@color/primary"
                        app:cornerRadius="8dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_meeting_point_selected"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="No seleccionado"
                        android:textColor="#9E9E9E"
                        android:textSize="14sp"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_location"
                        android:drawablePadding="8dp"
                        android:drawableTint="#9E9E9E"
                        tools:text="📍 Plaza de Armas, Cusco" />

                    <!-- Hora de encuentro (puede ser antes del inicio del tour) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_meeting_time"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Hora de Encuentro"
                        app:startIconDrawable="@drawable/ic_time"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:helperText="Hora en que los clientes deben llegar"
                        app:helperTextTextColor="#757575"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_meeting_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:focusable="false"
                            android:textSize="16sp"
                            android:textColor="#000000"
                            tools:text="08:30" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Idiomas Disponibles -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Header de sección -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_language"
                            app:tint="#3F51B5"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Idiomas" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Idiomas Disponibles"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Checkboxes para idiomas -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_spanish"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇪🇸 Español"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary"
                            android:checked="true" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_english"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇺🇸 Inglés"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_portuguese"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇧🇷 Portugués"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_french"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇫🇷 Francés"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_quechua"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇵🇪 Quechua"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_german"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇩🇪 Alemán"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_italian"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇮🇹 Italiano"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_chinese"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇨🇳 Chino"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_japanese"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇯🇵 Japonés"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_korean"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇰🇷 Coreano"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_russian"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇷🇺 Ruso"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />

                        <com.google.android.material.checkbox.MaterialCheckBox
                            android:id="@+id/cb_arabic"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="🇸🇦 Árabe"
                            android:textSize="15sp"
                            android:textColor="#2C2C2C"
                            android:paddingVertical="8dp"
                            app:buttonTint="@color/primary" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- FAB para guardar -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/btn_save_tour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:text="Crear Tour"
        app:icon="@drawable/ic_check_circle"
        app:backgroundTint="@color/primary"
        app:iconTint="@color/white"
        android:textColor="@color/white"
        app:elevation="8dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>