<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Client Avatar -->
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_client_avatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/ic_menu_myplaces"
            android:scaleType="centerCrop"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            android:background="@color/light_gray" />

        <!-- Chat Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_client_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="María González"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/tv_last_message_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Hace 5 min"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_tour_context"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="City Tour Lima Centro"
                android:textSize="14sp"
                android:textColor="@color/primary"
                android:layout_marginTop="2dp" />

            <TextView
                android:id="@+id/tv_last_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hola, tengo una pregunta sobre el tour de mañana..."
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginTop="4dp"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- Status Indicators -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:layout_marginStart="12dp">

            <!-- Unread Messages Badge -->
            <TextView
                android:id="@+id/tv_unread_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2"
                android:textSize="12sp"
                android:textColor="@color/white"
                android:background="@drawable/circle_red"
                android:gravity="center"
                android:minWidth="20dp"
                android:minHeight="20dp"
                android:visibility="visible" />

            <!-- Chat Status Indicator -->
            <View
                android:id="@+id/view_chat_status"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:background="@drawable/circle_green"
                android:layout_marginTop="8dp" />

            <TextView
                android:id="@+id/tv_chat_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Activo"
                android:textSize="10sp"
                android:textColor="@color/green"
                android:layout_marginTop="2dp" />
                
            <!-- Indicador de no leído -->
            <View
                android:id="@+id/view_unread_indicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/circle_primary"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
