<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray"
    android:padding="20dp"
    tools:context=".UserDisabledActivity">

    <!-- Main Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_disabled"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:cardCornerRadius="18dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/white"
        app:strokeWidth="1dp"
        app:strokeColor="#ECECEC"
        android:layout_marginHorizontal="6dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="22dp"
            android:gravity="center_horizontal">

            <!-- Icon bubble -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="12dp">

                <FrameLayout
                    android:layout_width="56dp"
                    android:layout_height="56dp">

                    <View
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:background="@drawable/bg_alert_orange" />

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_gravity="center"
                        android:src="@android:drawable/ic_dialog_alert"
                        app:tint="#FFFFFF" />
                </FrameLayout>

            </LinearLayout>

            <!-- Title (keep same ID) -->
            <TextView
                android:id="@+id/tv_disabled_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Cuenta deshabilitada"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="#202124"
                android:textAlignment="center" />

            <!-- Subtitle / helper -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tu acceso ha sido restringido. Si crees que es un error, contáctanos para ayudarte."
                android:textSize="13sp"
                android:textColor="#6B7280"
                android:textAlignment="center"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="14dp" />

            <!-- Reason (keep same ID) -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="14dp"
                app:cardElevation="0dp"
                app:cardBackgroundColor="#FAFAFA"
                app:strokeWidth="1dp"
                app:strokeColor="#EEEEEE"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="14dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Motivo"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:textColor="#6B7280"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tv_disabled_reason"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Estamos trabajando en ello. Por favor, contacta con soporte para más información."
                        android:textSize="15sp"
                        android:textColor="#202124"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Buttons row -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_contact_support"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Contactar soporte"
                android:textAllCaps="false"
                app:cornerRadius="12dp"
                android:layout_marginBottom="10dp"
                app:iconGravity="textStart"
                app:iconPadding="10dp"
                app:backgroundTint="@color/primary"
                android:textColor="@color/white"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_close"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Volver al login"
                android:textAllCaps="false"
                app:cornerRadius="12dp"
                app:icon="@drawable/ic_arrow_back_24"
                app:iconTint="@color/primary"
                app:iconGravity="textStart"
                app:iconPadding="10dp"
                app:backgroundTint="@color/white"
                android:textColor="@color/primary"
                />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
