<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- Left Color Strip -->
        <View
            android:id="@+id/view_status_indicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="@color/primary" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="City Tour Lima Centro"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:maxLines="1"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/tv_company_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Lima Adventure Tours"
                        android:textSize="12sp"
                        android:textColor="@color/gray"
                        android:layout_marginTop="2dp"
                        android:maxLines="1"
                        android:ellipsize="end" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_payment_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="S/. 180"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/green"
                    android:layout_marginStart="12dp" />

            </LinearLayout>

            <!-- Tour Details Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginEnd="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📅 Fecha"
                        android:textSize="11sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Mañana"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginEnd="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏰ Hora"
                        android:textSize="11sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="09:00 AM"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="👥 Grupo"
                        android:textSize="11sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_participants"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="6 pax"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Status Badge -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="PROGRAMADO"
                    android:textSize="11sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:background="@drawable/bg_status_confirmed"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@android:drawable/ic_menu_more"
                    android:tint="@color/gray" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

