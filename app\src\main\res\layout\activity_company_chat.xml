<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    android:clickable="true"
    android:focusableInTouchMode="true">

    <!-- ================= TOOLBAR ================= -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:navigationIcon="?attr/homeAsUpIndicator"
        app:navigationIconTint="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingEnd="12dp">

            <!-- Avatar Cliente -->
            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_client_avatar"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/ic_avatar_24"
                android:scaleType="centerCrop"
                android:background="@color/light_gray"
                app:shapeAppearanceOverlay="@style/CircleImageView" />

            <!-- Nombre + estado -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_client_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cusco Tour Adventure"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_client_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="En línea"
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:alpha="0.8" />
            </LinearLayout>


        </LinearLayout>
    </com.google.android.material.appbar.MaterialToolbar>

    <!-- ================= MESSAGES ================= -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_messages"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:padding="12dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/layout_message_input"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- ================= ATTACHMENT PREVIEW ================= -->
    <include
        android:id="@+id/layout_attachment_preview"
        layout="@layout/layout_attachment_preview"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/layout_message_input"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- ================= MESSAGE INPUT ================= -->
    <LinearLayout
        android:id="@+id/layout_message_input"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:background="#FFFFFF"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Adjuntar -->
        <ImageView
            android:id="@+id/iv_attach"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_attach"
            app:tint="@color/gray"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="@null" />

        <!-- Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:boxBackgroundMode="filled"
            app:boxBackgroundColor="#F2F2F2"
            app:boxCornerRadiusTopStart="24dp"
            app:boxCornerRadiusTopEnd="24dp"
            app:boxCornerRadiusBottomStart="24dp"
            app:boxCornerRadiusBottomEnd="24dp"
            app:boxStrokeWidth="0dp"
            app:boxStrokeWidthFocused="0dp"
            app:hintEnabled="false"
            app:boxStrokeColor="@android:color/transparent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Escribe un mensaje"
                android:inputType="textMultiLine|textCapSentences"
                android:maxLines="4"
                android:textColor="#2C2C2C"
                android:textColorHint="#9E9E9E"
                android:paddingStart="12dp"
                android:paddingEnd="12dp" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Enviar -->
        <ImageView
            android:id="@+id/iv_send_message"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_send"
            app:tint="@color/primary"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="6dp"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="@null" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
