<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_management_item"
    android:layout_width="200dp"
    android:layout_height="140dp"
    android:layout_marginEnd="12dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/iv_management_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_manage"
            app:tint="@color/primary"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/tv_management_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Gestión"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:textAlignment="center"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_management_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Descripción"
            android:textSize="12sp"
            android:textColor="@color/gray"
            android:textAlignment="center"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
