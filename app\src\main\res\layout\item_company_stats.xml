<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="#E0E0E0">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/white">

        <!-- Nombre de la Empresa -->
        <TextView
            android:id="@+id/tv_company_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Nombre de Empresa"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <!-- Ubicación -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_location"
                android:tint="#757575"
                android:layout_marginEnd="4dp"
                android:contentDescription="Ubicación" />

            <TextView
                android:id="@+id/tv_company_location"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Ubicación"
                android:textSize="14sp"
                android:textColor="#757575" />
        </LinearLayout>

        <!-- Divisor -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginBottom="12dp" />

        <!-- Estadísticas en Grid -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- Total Reservas -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_total_reservations"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/green" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Total"
                    android:textSize="11sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="2dp" />
            </LinearLayout>

            <!-- Divisor vertical -->
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#EEEEEE"
                android:layout_marginHorizontal="12dp" />

            <!-- Confirmadas -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_confirmed_reservations"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Confirmadas"
                    android:textSize="11sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="2dp" />
            </LinearLayout>

            <!-- Divisor vertical -->
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#EEEEEE"
                android:layout_marginHorizontal="12dp" />

            <!-- Completadas -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_completed_reservations"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="#4CAF50" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Completadas"
                    android:textSize="11sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="2dp" />
            </LinearLayout>

        </LinearLayout>

        <!-- Revenue -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="12dp"
            android:padding="12dp"
            android:background="#F5F5F5">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Ingresos Totales:"
                android:textSize="14sp"
                android:textColor="#757575" />

            <TextView
                android:id="@+id/tv_total_revenue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 0.00"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#4CAF50" />
        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

