<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    card_view:cardCornerRadius="8dp"
    card_view:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/tv_tour_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Nombre Tour"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/tv_tour_subinfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Duración • Precio"
            android:textSize="13sp"
            android:textColor="#757575"
            android:layout_marginTop="4dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
