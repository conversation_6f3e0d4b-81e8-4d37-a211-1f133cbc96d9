<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- ===== CHART CARD ===== -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_chart"
        android:layout_width="match_parent"
        android:layout_height="240dp"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="14dp"
        app:cardElevation="3dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ventas por Servicio"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#2C2C2C" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ordenadas de menor a mayor"
                android:textSize="12sp"
                android:textColor="@color/gray"
                android:layout_marginBottom="12dp" />

            <!-- Gráfico BarChart -->
            <com.github.mikephil.charting.charts.BarChart
                android:id="@+id/bar_chart_services"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <!-- ===== EMPTY STATE ===== -->
            <LinearLayout
                android:id="@+id/layout_empty_services"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_empty_box"
                    app:tint="@color/gray"
                    android:contentDescription="@null" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No hay ventas registradas"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#757575"
                    android:layout_marginTop="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Prueba cambiando el período"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="8dp" />
            </LinearLayout>

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- ===== LIST ===== -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_services_sales"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/card_chart"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
