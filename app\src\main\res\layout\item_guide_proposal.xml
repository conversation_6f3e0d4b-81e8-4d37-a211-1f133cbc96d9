<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with Tour and Guide Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_guide_photo"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_menu_myplaces"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/CircleImageView"
                android:background="@color/light_gray" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="City Tour Lima Centro"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/tv_guide_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Guía: Carlos Mendoza"
                    android:textSize="14sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_proposal_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="PENDIENTE"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/orange"
                android:background="@drawable/bg_status_pending"
                android:padding="6dp" />

        </LinearLayout>

        <!-- Proposal Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fecha del Tour"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_tour_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15 Dic, 2024"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Pago Propuesto"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_proposed_payment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="S/. 150.00"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/green"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Enviado"
                    android:textSize="12sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_sent_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10 Dic"
                    android:textSize="14sp"
                    android:textColor="@color/black"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons (only for pending proposals) -->
        <LinearLayout
            android:id="@+id/layout_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:visibility="visible">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cancel_proposal"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cancelar"
                android:textColor="@color/red"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_proposal"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Editar"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ver Detalles" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
