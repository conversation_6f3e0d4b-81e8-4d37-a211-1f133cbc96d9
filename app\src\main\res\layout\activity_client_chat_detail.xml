<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar with Company Info -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingEnd="16dp">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_company_avatar"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@android:drawable/ic_menu_myplaces"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/CircleImageView"
                android:background="@color/light_gray" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tours Cusco Adventures"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/tv_company_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="En línea"
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:alpha="0.8" />

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_call_company"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_call"
                android:tint="@color/white"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true"
                android:contentDescription="Llamar empresa" />

        </LinearLayout>

    </com.google.android.material.appbar.MaterialToolbar>

    <!-- Messages RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_messages"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/layout_message_input" />

    <!-- Message Input Layout -->
    <LinearLayout
        android:id="@+id/layout_message_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/primary"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_attach"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_camera"
            android:tint="@color/white"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="Adjuntar archivo" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Escribe un mensaje"
            android:layout_marginEnd="12dp"
            app:boxBackgroundMode="filled"
            app:boxBackgroundColor="@color/white"
            app:boxCornerRadiusTopStart="24dp"
            app:boxCornerRadiusTopEnd="24dp"
            app:boxCornerRadiusBottomStart="24dp"
            app:boxCornerRadiusBottomEnd="24dp"
            app:hintTextColor="@color/gray">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine|textCapSentences"
                android:maxLines="4"
                android:textColor="@color/black"
                android:textColorHint="@color/gray" />

        </com.google.android.material.textfield.TextInputLayout>

        <ImageView
            android:id="@+id/iv_send_message"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@android:drawable/ic_menu_send"
            android:tint="@color/white"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="Enviar mensaje" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
