<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- AppBar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Tipo de Registro"
            app:titleTextAppearance="@style/Toolbar.TitleText3"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:clipToPadding="false"
        android:paddingBottom="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="18dp"
            android:paddingEnd="18dp"
            android:paddingTop="18dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="18dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="¿Cómo deseas registrarte?"
                    android:textAlignment="center"
                    android:textColor="#202124"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Selecciona el tipo de usuario que mejor describe tu rol en DroidTour"
                    android:textAlignment="center"
                    android:textSize="14sp"
                    android:textColor="#6B7280" />

            </LinearLayout>

            <!-- Cliente -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_register_client"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="14dp"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E6E6E6"
                android:layout_marginHorizontal="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    >

                    <!-- Icon bubble -->
                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="14dp">

                        <View
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/icon_background_blue" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_person"
                            android:tint="#2196F3" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Turista"
                            android:textSize="17sp"
                            android:textStyle="bold"
                            android:textColor="#202124" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Reserva tours, explora destinos y disfruta de experiencias únicas"
                            android:textSize="13sp"
                            android:textColor="#6B7280"
                            android:layout_marginTop="6dp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:src="@drawable/ic_chevron_right"
                        app:tint="@color/primary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Guía de Turismo -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_register_guide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="14dp"
                android:clickable="true"
                android:focusable="true"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E6E6E6"
                android:layout_marginHorizontal="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <!-- Icon bubble -->
                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="14dp">

                        <View
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="@drawable/icon_background_green"/>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_mountain"
                            android:tint="@color/green" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Guía de Turismo"
                            android:textSize="17sp"
                            android:textStyle="bold"
                            android:textColor="#202124" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Guía tours, comparte tu conocimiento y gana dinero"
                            android:textSize="13sp"
                            android:textColor="#6B7280"
                            android:layout_marginTop="6dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Requiere aprobación para completar el registro"
                            android:textSize="12sp"
                            android:textColor="@color/orange"
                            android:layout_marginTop="6dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:src="@drawable/ic_chevron_right"
                        app:tint="@color/primary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Administrador de Empresa (mantengo tu lógica: invisible) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_register_admin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="14dp"
                android:clickable="true"
                android:focusable="true"
                android:visibility="invisible"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#E6E6E6">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <FrameLayout
                        android:layout_width="54dp"
                        android:layout_height="54dp"
                        android:layout_marginEnd="14dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="54dp"
                            android:layout_height="54dp"
                            app:cardCornerRadius="27dp"
                            app:cardElevation="0dp"
                            app:cardBackgroundColor="#1AFF9800" />

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:layout_gravity="center"
                            android:src="@android:drawable/ic_menu_manage"
                            app:tint="@color/orange" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Administrador de Empresa"
                            android:textSize="17sp"
                            android:textStyle="bold"
                            android:textColor="#202124" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Gestiona tu empresa de turismo, crea tours y administra reservas"
                            android:textSize="13sp"
                            android:textColor="#6B7280"
                            android:layout_marginTop="6dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Requiere aprobación del Superadmin"
                            android:textSize="12sp"
                            android:textColor="@color/orange"
                            android:layout_marginTop="6dp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:src="@drawable/ic_chevron_right"
                        app:tint="#B0B0B0" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Small helper text -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Puedes cambiar tu tipo de cuenta solo creando un nuevo usuario."
                android:textSize="12sp"
                android:textColor="#9AA0A6"
                android:textAlignment="center"
                android:layout_marginTop="8dp"
                android:paddingBottom="6dp"
                android:visibility="gone"/>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
