<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface">



    <!-- FAB para exportar reportes -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fab_export"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:text="Exportar"
        app:icon="@drawable/ic_download_24"
        app:backgroundTint="?attr/colorPrimary"
        app:iconTint="?attr/colorOnPrimary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>