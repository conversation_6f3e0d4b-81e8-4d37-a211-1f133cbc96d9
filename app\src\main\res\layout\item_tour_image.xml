<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="120dp"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Imagen del tour -->
        <ImageView
            android:id="@+id/iv_tour_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:contentDescription="Imagen del tour" />

        <!-- Botón para eliminar -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_remove_image"
            style="@style/Widget.Material3.Button.IconButton.Filled"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="top|end"
            android:layout_margin="4dp"
            app:backgroundTint="#B0000000"
            app:icon="@drawable/ic_close"
            app:iconSize="16dp"
            app:iconTint="@color/white" />

        <!-- Número de imagen -->
        <TextView
            android:id="@+id/tv_image_number"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="bottom|start"
            android:layout_margin="4dp"
            android:background="@drawable/circle_primary"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold" />

    </FrameLayout>

</com.google.android.material.card.MaterialCardView>
