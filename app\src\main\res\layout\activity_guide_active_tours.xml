<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Mis Tours"
        app:titleCentered="true"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_back"
        app:navigationIconTint="@color/white" />

    <!-- Scrollable Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:clipToPadding="false"
        android:paddingBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:paddingTop="10dp">

            <!-- Helper text -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Filtra y encuentra tus tours rápidamente."
                android:textSize="13sp"
                android:textColor="#6B7280"
                android:layout_marginBottom="12dp"
                android:layout_marginTop="10dp"/>

            <!-- Barra de búsqueda -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_search"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Buscar tours"
                app:startIconDrawable="@drawable/ic_search_form"

                app:layout_constraintTop_toBottomOf="@id/toolbar"
                app:startIconTint="@color/primary"
                app:endIconMode="clear_text"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary"
                app:boxCornerRadiusTopStart="8dp"
                app:boxCornerRadiusTopEnd="8dp"
                app:boxCornerRadiusBottomStart="8dp"
                app:boxCornerRadiusBottomEnd="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:textSize="16sp"
                    android:textColor="#000000" />
            </com.google.android.material.textfield.TextInputLayout>

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:singleLine="true"
                    app:chipSpacingHorizontal="10dp">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_todas"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Todas"
                        android:checkable="true"
                        android:checked="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_en_progreso"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="En progreso"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_programados"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Programados"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_completados"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Completados"
                        android:checkable="true"
                        app:checkedIconVisible="true" />

                </com.google.android.material.chip.ChipGroup>

            </HorizontalScrollView>

            <!-- Tours List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_my_tours"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                android:paddingBottom="6dp"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false" />

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
