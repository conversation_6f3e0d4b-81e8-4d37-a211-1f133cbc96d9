<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="1dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="#FFFFFF">

        <!-- Avatar/Icono de empresa con diseño mejorado -->
        <androidx.cardview.widget.CardView
            android:layout_width="56dp"
            android:layout_height="56dp"
            app:cardCornerRadius="28dp"
            app:cardElevation="0dp"
            android:layout_marginEnd="14dp"
            app:cardBackgroundColor="#F0F4F8">

            <ImageView
                android:id="@+id/iv_company_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_mountain"
                android:scaleType="centerCrop"
                android:layout_gravity="center" />

        </androidx.cardview.widget.CardView>

        <!-- Contenido del chat -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="8dp">

            <!-- Header con nombre y timestamp -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="6dp">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Tours Cusco Adventures"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="#1A1A1A"
                    android:fontFamily="sans-serif-medium"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/tv_timestamp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2:30 PM"
                    android:textSize="12sp"
                    android:textColor="#9E9E9E"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- Último mensaje -->
            <TextView
                android:id="@+id/tv_last_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Perfecto, nos vemos mañana a las 8 AM"
                android:textSize="14sp"
                android:textColor="#616161"
                android:maxLines="2"
                android:ellipsize="end"
                android:lineSpacingExtra="2dp"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- Indicador de mensajes no leídos -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cv_unread_badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="#4A90E2"
            android:visibility="gone"
            android:layout_gravity="center_vertical">

            <TextView
                android:id="@+id/tv_unread_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="24dp"
                android:minHeight="24dp"
                android:text="2"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="#FFFFFF"
                android:gravity="center"
                android:paddingHorizontal="6dp"
                android:paddingVertical="4dp"
                android:fontFamily="sans-serif-medium" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>