<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#FF7961">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#FF7961"
        android:padding="20dp">

        <TextView
            android:id="@+id/tvRegresar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Regresar"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:clickable="true"
            android:focusable="true"
            android:padding="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="16dp">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_person"
                android:tint="#FFFFFF"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Registro Turista"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginStart="12dp"/>
        </LinearLayout>
    </LinearLayout>




    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/rounded_scrollbox_background">



        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="24dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="#FFFFFF">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="32dp">

                    <!-- Nombres -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.FilledBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="Nombres"
                        app:boxBackgroundColor="#F8F8F8"
                        app:boxCornerRadiusBottomEnd="4dp"
                        app:boxCornerRadiusBottomStart="4dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp"
                        app:hintTextColor="#8E8E93">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etNombres"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName"
                            android:maxLength="40"
                            android:paddingTop="20dp"
                            android:paddingBottom="12dp"
                            android:textColor="#000000"
                            android:textSize="16sp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Apellidos -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.FilledBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="Apellidos"
                        app:boxBackgroundColor="#F8F8F8"
                        app:boxCornerRadiusBottomEnd="4dp"
                        app:boxCornerRadiusBottomStart="4dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp"
                        app:hintTextColor="#8E8E93">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etApellidos"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName"
                            android:maxLength="40"
                            android:paddingTop="20dp"
                            android:paddingBottom="12dp"
                            android:textColor="#000000"
                            android:textSize="16sp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Documento de Identidad -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="Documento de Identidad"
                        app:boxBackgroundColor="#F8F8F8"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp"
                        app:hintTextColor="#8E8E93">

                        <AutoCompleteTextView
                            android:id="@+id/etDocumento"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:paddingTop="20dp"
                            android:paddingBottom="12dp"
                            android:textColor="#000000"
                            android:textSize="16sp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Número de Documento -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.FilledBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="Número de Documento"
                        app:boxBackgroundColor="#F8F8F8"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp"
                        app:hintTextColor="#8E8E93">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etNumeroDocumento"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:paddingTop="20dp"
                            android:paddingBottom="12dp"
                            android:textColor="#000000"
                            android:textSize="16sp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- NÃºmero de Documento -->


                    <!-- Fecha de Nacimiento -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.FilledBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="Fecha de Nacimiento"
                        app:boxBackgroundColor="#F8F8F8"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp"
                        app:endIconDrawable="@android:drawable/ic_menu_my_calendar"
                        app:endIconMode="custom"
                        app:endIconTint="#8E8E93"
                        app:hintTextColor="#8E8E93">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etFechaNacimiento"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clickable="true"
                            android:focusable="false"
                            android:inputType="none"
                            android:paddingTop="20dp"
                            android:paddingBottom="12dp"
                            android:textColor="#000000"
                            android:textSize="16sp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Correo ElectrÃ³nico -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.FilledBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="Correo Electrónico"
                        app:boxBackgroundColor="#F8F8F8"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp"
                        app:hintTextColor="#8E8E93">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCorreo"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress"
                            android:paddingTop="20dp"
                            android:paddingBottom="12dp"
                            android:textColor="#000000"
                            android:textSize="16sp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Numero de TelÃ©fono -->
                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="32dp">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Número de Teléfono"
                            style="@style/Widget.Material3.TextInputLayout.FilledBox"
                            app:boxBackgroundColor="#F8F8F8"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp"
                            app:hintTextColor="#8E8E93">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etTelefono"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                android:inputType="phone"
                                android:maxLength="11"
                                android:gravity="start|center_vertical"
                                android:paddingTop="20dp"
                                android:paddingBottom="12dp"
                                android:paddingStart="110dp"/>
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.hbb20.CountryCodePicker
                            android:id="@+id/ccp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start|center_vertical"
                            android:layout_marginStart="12dp"
                            app:ccp_defaultNameCode="PE"
                            app:ccp_showFlag="true"
                            app:ccp_showNameCode="false"
                            app:ccp_showFullName="false"
                            app:ccp_showPhoneCode="true"
                            app:ccp_contentColor="#000000"
                            app:ccp_textSize="16sp"
                            app:ccp_arrowSize="12dp"
                            app:ccp_customMasterCountries="PE,ES,MX,AR,CO,CL,EC,BO,VE,UY,PY,BR" />
                    </FrameLayout>



                    <!-- BotÃ³n Siguiente -->

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>



            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSiguiente"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:backgroundTint="#007AFF"
                android:layout_marginTop="5dp"
                android:elevation="0dp"
                android:text="Siguiente"
                android:textAllCaps="false"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                app:cornerRadius="28dp"
                app:icon="@android:drawable/ic_media_play"
                app:iconGravity="end"
                app:iconTint="#FFFFFF" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>