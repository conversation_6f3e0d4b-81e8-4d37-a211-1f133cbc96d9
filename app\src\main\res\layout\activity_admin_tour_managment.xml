<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Gestion de Tours"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"/>

        <!-- Search Bar -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/searchInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Buscar tours..."
                app:startIconDrawable="@drawable/ic_search_form"
                app:boxBackgroundMode="outline"
                app:boxCornerRadiusTopStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusBottomEnd="12dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/searchEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Chips de Filtro -->
            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:scrollbars="none">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/filterChipGroup"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:singleSelection="true"
                    app:selectionRequired="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipAll"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Todos"
                        android:checked="true"
                        app:chipBackgroundColor="@color/chip_background_selector"
                        app:chipStrokeWidth="0dp" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipActive"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Activos"
                        app:chipBackgroundColor="@color/chip_background_selector"
                        app:chipStrokeWidth="0dp" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipPending"
                        style="@style/Widget.MaterialComponents.Chip.Choice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Sin Guía"
                        app:chipBackgroundColor="@color/chip_background_selector"
                        app:chipStrokeWidth="0dp" />

                </com.google.android.material.chip.ChipGroup>

            </HorizontalScrollView>

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- RecyclerView para Tours -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/toursRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:paddingBottom="80dp"
                tools:listitem="@layout/item_admin_tour_card" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@drawable/ic_empty_box"
                    android:alpha="0.3"
                    android:contentDescription="No hay Tours" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="No hay tours registrados"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:textAlignment="center" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Crea tu primer tour presionando el botón +"
                    android:textSize="14sp"
                    android:textColor="#999999"
                    android:textAlignment="center" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabCreateTour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:text="Nuevo Tour"
        android:textColor="@android:color/white"
        app:icon="@drawable/ic_add"
        app:iconTint="@android:color/white"
        app:backgroundTint="#2196F3"
        app:elevation="6dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>