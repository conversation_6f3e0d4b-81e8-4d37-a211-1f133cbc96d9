<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#825252">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:id="@+id/tvRegresar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Regresar"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:clickable="true"
            android:focusable="true"
            android:padding="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="16dp">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_mountain"
                app:tint="#FFFFFF"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Registro Guía de Turismo"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginStart="12dp"/>
        </LinearLayout>
    </LinearLayout>

    <!-- Contenedor blanco con esquinas redondeadas -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardElevation="0dp"
        app:cardCornerRadius="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:padding="32dp">

            <!-- Texto -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Es momento de subir una foto!"
                android:textColor="#666666"
                android:textSize="16sp"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="32dp"/>

            <!-- Foto de perfil con botón editar -->
            <FrameLayout
                android:layout_width="260dp"
                android:layout_height="260dp"
                android:layout_marginBottom="24dp">

                <ImageView
                    android:id="@+id/ivProfilePhoto"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_person"
                    android:background="@drawable/circle_background"
                    android:contentDescription="Foto de perfil"/>

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/fabEditPhoto"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|end"
                    android:layout_marginEnd="8dp"
                    android:layout_marginBottom="8dp"
                    app:fabSize="mini"
                    app:backgroundTint="#FFFFFF"
                    app:tint="@color/primary"
                    android:src="@android:drawable/ic_menu_edit"/>
            </FrameLayout>

            <!-- Nombre completo -->
            <TextView
                android:id="@+id/tvFullName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Juan Ernesto\nCarrasco Guerrero"
                android:textColor="#000000"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="8dp"/>

            <!-- Email -->
            <TextView
                android:id="@+id/tvEmail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="<EMAIL>"
                android:textColor="#999999"
                android:textSize="14sp"
                android:layout_marginBottom="48dp"/>

            <Space
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"/>

            <!-- Botón Siguiente -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSiguiente"
                android:layout_width="wrap_content"
                android:layout_height="56dp"
                android:text="Siguiente"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textAllCaps="false"
                android:paddingStart="32dp"
                android:paddingEnd="32dp"
                app:cornerRadius="28dp"
                app:icon="@android:drawable/ic_media_play"
                app:iconGravity="end"
                app:iconTint="#FFFFFF"
                android:backgroundTint="#2196F3"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>