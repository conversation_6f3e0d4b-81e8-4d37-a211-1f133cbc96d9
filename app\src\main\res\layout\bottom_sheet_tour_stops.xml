<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Handle bar -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"
        android:background="@color/divider" />

    <!-- Tour Name -->
    <TextView
        android:id="@+id/tv_tour_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="City Tour Lima"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="12dp" />

    <!-- Stats Card -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/light_gray"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_total_stops"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Total Paradas"
                android:textSize="11sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/divider" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_completed_stops"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/holo_green_dark" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Confirmadas"
                android:textSize="11sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

    <!-- Meeting Point Card -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/light_gray"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_dialog_map"
                android:tint="@android:color/holo_blue_dark" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Punto de Encuentro"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_meeting_point"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Plaza Mayor"
            android:textSize="13sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="6dp"
            android:layout_marginStart="32dp" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider"
        android:layout_marginBottom="8dp" />

    <!-- Stops List -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Paradas del Tour"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_stops"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false" />

</LinearLayout>

</androidx.core.widget.NestedScrollView>
