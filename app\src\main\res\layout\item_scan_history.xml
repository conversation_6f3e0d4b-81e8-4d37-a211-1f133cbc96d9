<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="1dp"
    android:layout_marginHorizontal="4dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- Scan Type Icon -->
        <LinearLayout
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/circle_primary"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_scan_type"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@android:drawable/ic_menu_camera"
                android:tint="@color/white" />

        </LinearLayout>

        <!-- Client Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_client_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Ana García Pérez"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp">

                <TextView
                    android:id="@+id/tv_scan_type_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Check-in"
                    android:textSize="10sp"
                    android:textColor="@color/green"
                    android:textStyle="bold"
                    android:background="@drawable/rounded_background_light"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="1dp" />

                <TextView
                    android:id="@+id/tv_scan_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="• 10:15 AM"
                    android:textSize="10sp"
                    android:textColor="@color/gray"
                    android:layout_marginStart="6dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Status Indicator -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <View
                android:id="@+id/view_status_indicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/circle_green" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✓"
                android:textSize="12sp"
                android:textColor="@color/green"
                android:textStyle="bold"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
