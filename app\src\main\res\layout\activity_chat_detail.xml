<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- Toolbar with Client Info -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:navigationIcon="@android:drawable/ic_menu_revert"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingEnd="16dp">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_client_avatar"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@android:drawable/ic_menu_myplaces"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/CircleImageView"
                android:background="@color/light_gray" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_client_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="María González"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/tv_client_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="En línea"
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:alpha="0.8" />

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_call_client"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_call"
                android:tint="@color/white"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true"
                android:contentDescription="Llamar cliente" />

        </LinearLayout>

    </com.google.android.material.appbar.MaterialToolbar>

    <!-- Tour Context Banner -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_tour_context"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/primary"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@android:drawable/ic_menu_mapmode"
                android:tint="@color/white" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_tour_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="City Tour Lima Centro"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/tv_tour_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mañana, 15 Dic • 09:00 AM"
                    android:textSize="12sp"
                    android:textColor="@color/white"
                    android:alpha="0.9" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_booking_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="CONFIRMADO"
                android:textSize="10sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:background="@drawable/bg_status_confirmed"
                android:padding="4dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Messages RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_messages"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/card_tour_context"
        app:layout_constraintBottom_toTopOf="@id/layout_message_input" />

    <!-- Message Input Layout -->
    <LinearLayout
        android:id="@+id/layout_message_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Escribir mensaje..."
            android:layout_marginEnd="12dp"
            app:boxBackgroundMode="outline"
            app:boxCornerRadiusTopStart="24dp"
            app:boxCornerRadiusTopEnd="24dp"
            app:boxCornerRadiusBottomStart="24dp"
            app:boxCornerRadiusBottomEnd="24dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine|textCapSentences"
                android:maxLines="4" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_send_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@android:drawable/ic_menu_send"
            android:contentDescription="Enviar mensaje"
            app:fabSize="mini" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
