<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Notificaciones"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleCentered="true"
        app:titleTextColor="@color/white"
        app:navigationIcon="?attr/homeAsUpIndicator"
        app:navigationIconTint="@color/white">

        <!-- Menu icon for mark all as read -->
        <ImageView
            android:id="@+id/iv_mark_all_read"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="end"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_done_all"
            app:tint="@color/white"
            android:padding="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="@string/mark_all_read" />

    </com.google.android.material.appbar.MaterialToolbar>

    <!-- Filter Tabs -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="2dp"
        app:tabMode="fixed"
        app:tabGravity="fill"
        app:tabSelectedTextColor="@color/primary"
        app:tabTextColor="#757575"
        app:tabIndicatorColor="@color/primary"
        app:tabIndicatorHeight="3dp">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tab_all" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tab_unread" />

    </com.google.android.material.tabs.TabLayout>

    <!-- Content Container -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- RecyclerView for notifications -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_notifications"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:clipChildren="false"
            android:paddingTop="8dp"
            android:paddingBottom="16dp"
            android:scrollbars="vertical" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/empty_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_notifications_empty"
                app:tint="#BDBDBD"
                android:alpha="0.6"
                android:contentDescription="@string/no_notifications" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_notifications"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:layout_marginTop="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/empty_notifications_text"
                android:textSize="14sp"
                android:textColor="#999999"
                android:textAlignment="center"
                android:gravity="center"
                android:layout_marginTop="8dp" />

        </LinearLayout>

        <!-- Loading State -->
        <LinearLayout
            android:id="@+id/loading_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ProgressBar
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:indeterminateTint="@color/primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/loading_notifications"
                android:textSize="14sp"
                android:textColor="#757575"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>