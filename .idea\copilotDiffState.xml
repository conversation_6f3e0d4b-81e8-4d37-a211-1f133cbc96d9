<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotDiffPersistence">
    <option name="pendingDiffs">
      <map>
        <entry key="$PROJECT_DIR$/app/src/main/res/layout/activity_select_language_register.xml">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/app/src/main/res/layout/activity_select_language_register.xml" />
              <option name="originalContent" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;&#10;&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;&#10;    xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;&#10;    android:layout_width=&quot;match_parent&quot;&#10;    android:layout_height=&quot;match_parent&quot;&#10;    android:orientation=&quot;vertical&quot;&#10;    android:background=&quot;#825252&quot;&gt;&#10;&#10;    &lt;!-- Header --&gt;&#10;    &lt;LinearLayout&#10;        android:layout_width=&quot;match_parent&quot;&#10;        android:layout_height=&quot;wrap_content&quot;&#10;        android:orientation=&quot;vertical&quot;&#10;        android:padding=&quot;20dp&quot;&gt;&#10;&#10;        &lt;TextView&#10;            android:id=&quot;@+id/tvRegresar&quot;&#10;            android:layout_width=&quot;wrap_content&quot;&#10;            android:layout_height=&quot;wrap_content&quot;&#10;            android:text=&quot;Regresar&quot;&#10;            android:textColor=&quot;#FFFFFF&quot;&#10;            android:textSize=&quot;14sp&quot;&#10;            android:clickable=&quot;true&quot;&#10;            android:focusable=&quot;true&quot;&#10;            android:padding=&quot;8dp&quot;/&gt;&#10;&#10;        &lt;LinearLayout&#10;            android:layout_width=&quot;match_parent&quot;&#10;            android:layout_height=&quot;wrap_content&quot;&#10;            android:orientation=&quot;horizontal&quot;&#10;            android:gravity=&quot;center_vertical&quot;&#10;            android:layout_marginTop=&quot;16dp&quot;&gt;&#10;&#10;            &lt;ImageView&#10;                android:layout_width=&quot;40dp&quot;&#10;                android:layout_height=&quot;40dp&quot;&#10;                android:src=&quot;@drawable/ic_mountain&quot;&#10;                android:tint=&quot;#FFFFFF&quot;/&gt;&#10;&#10;            &lt;TextView&#10;                android:layout_width=&quot;wrap_content&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:text=&quot;Registro Guía de Turismo&quot;&#10;                android:textColor=&quot;#FFFFFF&quot;&#10;                android:textSize=&quot;24sp&quot;&#10;                android:textStyle=&quot;bold&quot;&#10;                android:layout_marginStart=&quot;12dp&quot;/&gt;&#10;        &lt;/LinearLayout&gt;&#10;    &lt;/LinearLayout&gt;&#10;&#10;    &lt;!-- Contenedor blanco con esquinas redondeadas --&gt;&#10;    &lt;androidx.cardview.widget.CardView&#10;        android:layout_width=&quot;match_parent&quot;&#10;        android:layout_height=&quot;match_parent&quot;&#10;        app:cardElevation=&quot;0dp&quot;&#10;        app:cardCornerRadius=&quot;20dp&quot;&gt;&#10;&#10;        &lt;LinearLayout&#10;            android:layout_width=&quot;match_parent&quot;&#10;            android:layout_height=&quot;match_parent&quot;&#10;            android:orientation=&quot;vertical&quot;&#10;            android:padding=&quot;24dp&quot;&gt;&#10;&#10;            &lt;!-- Título --&gt;&#10;            &lt;TextView&#10;                android:layout_width=&quot;wrap_content&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:text=&quot;Selecciona los idiomas que domina&quot;&#10;                android:textColor=&quot;#666666&quot;&#10;                android:textSize=&quot;16sp&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;/&gt;&#10;&#10;            &lt;!-- Buscador de idiomas --&gt;&#10;            &lt;com.google.android.material.textfield.TextInputLayout&#10;                style=&quot;@style/Widget.Material3.TextInputLayout.FilledBox&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;&#10;                android:hint=&quot;Busca un idioma&quot;&#10;                app:boxBackgroundColor=&quot;#F8F8F8&quot;&#10;                app:boxCornerRadiusBottomEnd=&quot;8dp&quot;&#10;                app:boxCornerRadiusBottomStart=&quot;8dp&quot;&#10;                app:boxCornerRadiusTopEnd=&quot;8dp&quot;&#10;                app:boxCornerRadiusTopStart=&quot;8dp&quot;&#10;                app:startIconDrawable=&quot;@android:drawable/ic_menu_search&quot;&#10;                app:startIconTint=&quot;#8E8E93&quot;&#10;                app:hintTextColor=&quot;#8E8E93&quot;&gt;&#10;&#10;                &lt;com.google.android.material.textfield.TextInputEditText&#10;                    android:id=&quot;@+id/etBuscarIdioma&quot;&#10;                    android:layout_width=&quot;match_parent&quot;&#10;                    android:layout_height=&quot;wrap_content&quot;&#10;                    android:inputType=&quot;text&quot;&#10;                    android:textColor=&quot;#000000&quot;&#10;                    android:textSize=&quot;16sp&quot;/&gt;&#10;            &lt;/com.google.android.material.textfield.TextInputLayout&gt;&#10;&#10;            &lt;!-- Contenedor de Chips Seleccionados --&gt;&#10;            &lt;com.google.android.material.chip.ChipGroup&#10;                android:id=&quot;@+id/chipGroupSelectedLanguages&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;&#10;                app:chipSpacing=&quot;8dp&quot;&#10;                app:singleLine=&quot;false&quot;/&gt;&#10;&#10;            &lt;!-- Divisor --&gt;&#10;            &lt;View&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;1dp&quot;&#10;                android:background=&quot;#E0E0E0&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;/&gt;&#10;&#10;            &lt;!-- Lista de idiomas disponibles --&gt;&#10;            &lt;androidx.recyclerview.widget.RecyclerView&#10;                android:id=&quot;@+id/rvLanguages&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;0dp&quot;&#10;                android:layout_weight=&quot;1&quot;&#10;                android:scrollbars=&quot;vertical&quot;&#10;                android:clipToPadding=&quot;false&quot;&#10;                android:paddingBottom=&quot;8dp&quot;/&gt;&#10;&#10;            &lt;!-- Botón Siguiente --&gt;&#10;            &lt;com.google.android.material.button.MaterialButton&#10;                android:id=&quot;@+id/btnSiguiente&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;56dp&quot;&#10;                android:text=&quot;Siguiente&quot;&#10;                android:textColor=&quot;#FFFFFF&quot;&#10;                android:textSize=&quot;16sp&quot;&#10;                android:textAllCaps=&quot;false&quot;&#10;                android:layout_marginTop=&quot;16dp&quot;&#10;                app:cornerRadius=&quot;28dp&quot;&#10;                app:icon=&quot;@android:drawable/ic_media_play&quot;&#10;                app:iconGravity=&quot;end&quot;&#10;                app:iconTint=&quot;#FFFFFF&quot;&#10;                android:backgroundTint=&quot;#2196F3&quot;&#10;                android:enabled=&quot;false&quot;/&gt;&#10;        &lt;/LinearLayout&gt;&#10;    &lt;/androidx.cardview.widget.CardView&gt;&#10;&#10;&lt;/LinearLayout&gt;" />
              <option name="updatedContent" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;&#10;&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;&#10;    xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;&#10;    android:layout_width=&quot;match_parent&quot;&#10;    android:layout_height=&quot;match_parent&quot;&#10;    android:orientation=&quot;vertical&quot;&#10;    android:background=&quot;#825252&quot;&gt;&#10;&#10;    &lt;!-- Header --&gt;&#10;    &lt;LinearLayout&#10;        android:layout_width=&quot;match_parent&quot;&#10;        android:layout_height=&quot;wrap_content&quot;&#10;        android:orientation=&quot;vertical&quot;&#10;        android:padding=&quot;20dp&quot;&gt;&#10;&#10;        &lt;TextView&#10;            android:id=&quot;@+id/tvRegresar&quot;&#10;            android:layout_width=&quot;wrap_content&quot;&#10;            android:layout_height=&quot;wrap_content&quot;&#10;            android:text=&quot;Regresar&quot;&#10;            android:textColor=&quot;#FFFFFF&quot;&#10;            android:textSize=&quot;14sp&quot;&#10;            android:clickable=&quot;true&quot;&#10;            android:focusable=&quot;true&quot;&#10;            android:padding=&quot;8dp&quot;/&gt;&#10;&#10;        &lt;LinearLayout&#10;            android:layout_width=&quot;match_parent&quot;&#10;            android:layout_height=&quot;wrap_content&quot;&#10;            android:orientation=&quot;horizontal&quot;&#10;            android:gravity=&quot;center_vertical&quot;&#10;            android:layout_marginTop=&quot;16dp&quot;&gt;&#10;&#10;            &lt;ImageView&#10;                android:layout_width=&quot;40dp&quot;&#10;                android:layout_height=&quot;40dp&quot;&#10;                android:src=&quot;@drawable/ic_mountain&quot;&#10;                android:tint=&quot;#FFFFFF&quot;/&gt;&#10;&#10;            &lt;TextView&#10;                android:layout_width=&quot;wrap_content&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:text=&quot;Registro Guía de Turismo&quot;&#10;                android:textColor=&quot;#FFFFFF&quot;&#10;                android:textSize=&quot;24sp&quot;&#10;                android:textStyle=&quot;bold&quot;&#10;                android:layout_marginStart=&quot;12dp&quot;/&gt;&#10;        &lt;/LinearLayout&gt;&#10;    &lt;/LinearLayout&gt;&#10;&#10;    &lt;!-- Contenedor blanco con esquinas redondeadas --&gt;&#10;    &lt;androidx.cardview.widget.CardView&#10;        android:layout_width=&quot;match_parent&quot;&#10;        android:layout_height=&quot;match_parent&quot;&#10;        app:cardElevation=&quot;0dp&quot;&#10;        app:cardCornerRadius=&quot;20dp&quot;&gt;&#10;&#10;        &lt;LinearLayout&#10;            android:layout_width=&quot;match_parent&quot;&#10;            android:layout_height=&quot;match_parent&quot;&#10;            android:orientation=&quot;vertical&quot;&#10;            android:padding=&quot;24dp&quot;&gt;&#10;&#10;            &lt;!-- Título --&gt;&#10;            &lt;TextView&#10;                android:layout_width=&quot;wrap_content&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:text=&quot;Selecciona los idiomas que domina&quot;&#10;                android:textColor=&quot;#666666&quot;&#10;                android:textSize=&quot;16sp&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;/&gt;&#10;&#10;            &lt;!-- Buscador de idiomas --&gt;&#10;            &lt;com.google.android.material.textfield.TextInputLayout&#10;                style=&quot;@style/Widget.Material3.TextInputLayout.FilledBox&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;&#10;                android:hint=&quot;Busca un idioma&quot;&#10;                app:boxBackgroundColor=&quot;#F8F8F8&quot;&#10;                app:boxCornerRadiusBottomEnd=&quot;8dp&quot;&#10;                app:boxCornerRadiusBottomStart=&quot;8dp&quot;&#10;                app:boxCornerRadiusTopEnd=&quot;8dp&quot;&#10;                app:boxCornerRadiusTopStart=&quot;8dp&quot;&#10;                app:startIconDrawable=&quot;@android:drawable/ic_menu_search&quot;&#10;                app:startIconTint=&quot;#8E8E93&quot;&#10;                app:hintTextColor=&quot;#8E8E93&quot;&gt;&#10;&#10;                &lt;com.google.android.material.textfield.TextInputEditText&#10;                    android:id=&quot;@+id/etBuscarIdioma&quot;&#10;                    android:layout_width=&quot;match_parent&quot;&#10;                    android:layout_height=&quot;wrap_content&quot;&#10;                    android:inputType=&quot;text&quot;&#10;                    android:textColor=&quot;#000000&quot;&#10;                    android:textSize=&quot;16sp&quot;/&gt;&#10;            &lt;/com.google.android.material.textfield.TextInputLayout&gt;&#10;&#10;            &lt;!-- Contenedor de Chips Seleccionados --&gt;&#10;            &lt;com.google.android.material.chip.ChipGroup&#10;                android:id=&quot;@+id/chipGroupSelectedLanguages&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;wrap_content&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;&#10;                app:chipSpacing=&quot;8dp&quot;&#10;                app:singleLine=&quot;false&quot;/&gt;&#10;&#10;            &lt;!-- Divisor --&gt;&#10;            &lt;View&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;1dp&quot;&#10;                android:background=&quot;#E0E0E0&quot;&#10;                android:layout_marginBottom=&quot;16dp&quot;/&gt;&#10;&#10;            &lt;!-- Lista de idiomas disponibles --&gt;&#10;            &lt;androidx.recyclerview.widget.RecyclerView&#10;                android:id=&quot;@+id/rvLanguages&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;0dp&quot;&#10;                android:layout_weight=&quot;1&quot;&#10;                android:scrollbars=&quot;vertical&quot;&#10;                android:clipToPadding=&quot;false&quot;&#10;                android:paddingBottom=&quot;8dp&quot;/&gt;&#10;&#10;            &lt;!-- Botón Siguiente --&gt;&#10;            &lt;com.google.android.material.button.MaterialButton&#10;                android:id=&quot;@+id/btnSiguiente&quot;&#10;                android:layout_width=&quot;match_parent&quot;&#10;                android:layout_height=&quot;56dp&quot;&#10;                android:text=&quot;Siguiente&quot;&#10;                android:textColor=&quot;#FFFFFF&quot;&#10;                android:textSize=&quot;16sp&quot;&#10;                android:textAllCaps=&quot;false&quot;&#10;                android:layout_marginTop=&quot;16dp&quot;&#10;                app:cornerRadius=&quot;28dp&quot;&#10;                app:icon=&quot;@android:drawable/ic_media_play&quot;&#10;                app:iconGravity=&quot;end&quot;&#10;                app:iconTint=&quot;#FFFFFF&quot;&#10;                android:backgroundTint=&quot;#2196F3&quot;&#10;                android:enabled=&quot;false&quot;/&gt;&#10;        &lt;/LinearLayout&gt;&#10;    &lt;/androidx.cardview.widget.CardView&gt;&#10;&#10;&lt;/LinearLayout&gt;" />
            </PendingDiffInfo>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>