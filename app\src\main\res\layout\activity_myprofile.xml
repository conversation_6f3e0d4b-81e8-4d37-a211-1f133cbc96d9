<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- AppBarLayout para manejar correctamente el scroll -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Mi Perfil"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:paddingBottom="16dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="80dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/profile_header_bg"
                android:paddingTop="32dp"
                android:paddingBottom="24dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <!-- Foto de perfil con botón editar -->
                <FrameLayout
                    android:id="@+id/profile_image_container"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_centerHorizontal="true">

                    <View
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:background="@drawable/profile_pink_circle" />

                    <ImageView
                        android:id="@+id/profile_image"
                        android:layout_width="108dp"
                        android:layout_height="108dp"
                        android:layout_gravity="center"
                        android:background="@drawable/profile_image_circle"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_avatar_24" />


                </FrameLayout>

                <!-- Información básica del usuario -->
                <LinearLayout
                    android:id="@+id/user_info_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/profile_image_container"
                    android:layout_marginTop="20dp"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:paddingBottom="8dp">

                    <TextView
                        android:id="@+id/tv_user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="María González"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="#FFFFFF"
                        android:maxLines="1"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/tv_user_email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textSize="15sp"
                        android:textColor="#E8E8E8"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:layout_marginTop="8dp" />

                    <TextView
                        android:id="@+id/tv_user_role"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Cliente"
                        android:textSize="13sp"
                        android:textColor="#FFFFFF"
                        android:background="@drawable/role_chip_bg"
                        android:paddingHorizontal="20dp"
                        android:paddingVertical="8dp"
                        android:layout_marginTop="16dp" />
                </LinearLayout>
            </RelativeLayout>

            <!-- Contenido de las tarjetas -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Sección: Información Personal -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="3dp"
                    android:layout_marginHorizontal="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Header de la sección -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingBottom="8dp">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_person"
                                android:tint="#2196F3"
                                android:layout_marginEnd="12dp"/>

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Información Personal"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C" />
                        </LinearLayout>

                        <!-- Separador -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#EEEEEE"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="8dp"/>

                        <!-- Datos personales -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <!-- Nombres -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:paddingVertical="12px">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Nombres"
                                    android:textSize="15sp"
                                    android:textColor="#757575" />

                                <TextView
                                    android:id="@+id/tv_first_name"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="María"
                                    android:textSize="15sp"
                                    android:textColor="#2C2C2C"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#F5F5F5"/>

                            <!-- Apellidos -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:paddingVertical="12px">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Apellidos"
                                    android:textSize="15sp"
                                    android:textColor="#757575" />

                                <TextView
                                    android:id="@+id/tv_last_name"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="González López"
                                    android:textSize="15sp"
                                    android:textColor="#2C2C2C"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <View
                                android:id="@+id/divider_before_birth_date"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#F5F5F5"/>

                            <!-- Fecha de Nacimiento -->
                            <LinearLayout
                                android:id="@+id/row_birth_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:paddingVertical="12px">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Fecha de Nacimiento"
                                    android:textSize="15sp"
                                    android:textColor="#757575" />

                                <TextView
                                    android:id="@+id/tv_birth_date"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="15/03/1990"
                                    android:textSize="15sp"
                                    android:textColor="#2C2C2C"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <View
                                android:id="@+id/divider_after_birth_date"
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#F5F5F5"/>

                            <!-- Tipo de Documento -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:paddingVertical="12px">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Tipo de Documento"
                                    android:textSize="15sp"
                                    android:textColor="#757575" />

                                <TextView
                                    android:id="@+id/tv_document_type"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="DNI"
                                    android:textSize="15sp"
                                    android:textColor="#2C2C2C"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#F5F5F5"/>

                            <!-- Número de Documento -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:paddingVertical="12px">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Número de Documento"
                                    android:textSize="15sp"
                                    android:textColor="#757575" />

                                <TextView
                                    android:id="@+id/tv_document_number"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="12345678"
                                    android:textSize="15sp"
                                    android:textColor="#2C2C2C"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#F5F5F5"/>

                            <!-- Teléfono -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:paddingVertical="12px">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="Teléfono"
                                    android:textSize="15sp"
                                    android:textColor="#757575" />

                                <TextView
                                    android:id="@+id/tv_phone"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="+51 987 654 321"
                                    android:textSize="15sp"
                                    android:textColor="#2C2C2C"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- Sección: Idiomas (Solo para Guías) -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_languages"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="3dp"
                    android:layout_marginHorizontal="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingBottom="8dp">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_language"
                                android:tint="#FF9800"
                                android:layout_marginEnd="12dp"/>

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Idiomas que Hablo"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#EEEEEE"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="12dp"/>

                        <com.google.android.material.chip.ChipGroup
                            android:id="@+id/chip_group_languages"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:chipSpacingHorizontal="8dp"
                            app:chipSpacingVertical="8dp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- Sección: Estadísticas -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_statistics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="3dp"
                    android:layout_marginHorizontal="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingBottom="8dp">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_stats"
                                android:tint="#4CAF50"
                                android:layout_marginEnd="12dp"/>

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Mis Estadísticas"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="#2C2C2C" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#EEEEEE"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="16dp"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:baselineAligned="false">

                            <!-- Tours Reservados -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/tv_tours_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="12"
                                    android:textSize="32sp"
                                    android:textStyle="bold"
                                    android:textColor="#2196F3" />

                                <TextView
                                    android:id="@+id/tv_stat_label_1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Tours\nReservados"
                                    android:textSize="13sp"
                                    android:textColor="#757575"
                                    android:gravity="center"
                                    android:textAlignment="center"
                                    android:layout_marginTop="6dp"/>
                            </LinearLayout>

                            <!-- Valoraciones -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/tv_rating"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="4.8"
                                    android:textSize="32sp"
                                    android:textStyle="bold"
                                    android:textColor="#FF9800" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Valoración"
                                    android:textSize="13sp"
                                    android:textColor="#757575"
                                    android:gravity="center"
                                    android:layout_marginTop="6dp"/>
                            </LinearLayout>

                            <!-- Miembro desde -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/tv_member_since"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="2024"
                                    android:textSize="32sp"
                                    android:textStyle="bold"
                                    android:textColor="#9C27B0" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Miembro\ndesde"
                                    android:textSize="13sp"
                                    android:textColor="#757575"
                                    android:gravity="center"
                                    android:textAlignment="center"
                                    android:layout_marginTop="6dp"/>
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <!-- FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_edit_white"
        app:backgroundTint="@color/primary"
        app:tint="@color/white"
        app:elevation="8dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>