<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Imágenes del servicio -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="20dp"
            android:weightSum="2">

            <!-- Imagen 1 -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <ImageView
                    android:id="@+id/iv_service_image1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:contentDescription="Imagen del servicio 1"
                    android:background="#E0E0E0" />
            </androidx.cardview.widget.CardView>

            <!-- Imagen 2 -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="180dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <ImageView
                    android:id="@+id/iv_service_image2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:contentDescription="Imagen del servicio 2"
                    android:background="#E0E0E0" />
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <!-- Nombre del servicio -->
        <TextView
            android:id="@+id/tv_service_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Nombre del Servicio"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#1A1A1A"
            android:layout_marginBottom="12dp" />

        <!-- Precio -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardBackgroundColor="#E8F5E9"
            app:cardCornerRadius="10dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tv_service_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="S/. 0.00"
                    android:textSize="26sp"
                    android:textStyle="bold"
                    android:textColor="#2E7D32" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Descripción -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Descripción"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#1A1A1A"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/tv_service_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Descripción del servicio"
            android:textSize="15sp"
            android:textColor="#424242"
            android:lineSpacingExtra="6dp"
            android:lineSpacingMultiplier="1.2"
            android:layout_marginBottom="8dp" />

    </LinearLayout>

</ScrollView>
