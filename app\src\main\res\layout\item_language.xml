<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- Emoji de la bandera -->
    <TextView
        android:id="@+id/tvFlagEmoji"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:gravity="center"
        android:text="🇺🇸"
        android:textSize="32sp"/>

    <TextView
        android:id="@+id/tvLanguageName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:text="Inglés"
        android:textColor="#000000"
        android:textSize="16sp"/>

    <!-- Checkbox de selección -->
    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/cbSelected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        app:buttonTint="#2196F3"/>

</LinearLayout>