<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background">

    <!-- Filt<PERSON> de Estado -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardElevation="2dp"
        app:cardCornerRadius="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Filtrar por Estado"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_refresh"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:contentDescription="Actualizar"
                    app:icon="@android:drawable/ic_popup_sync"
                    app:iconTint="@color/primary" />

            </LinearLayout>

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:singleSelection="true"
                app:chipSpacingHorizontal="8dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Todas"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_pending"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Pendientes"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_accepted"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Aceptadas"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_rejected"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Rechazadas"
                    style="@style/Widget.Material3.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_cancelled"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Canceladas"
                    style="@style/Widget.Material3.Chip.Filter" />

            </com.google.android.material.chip.ChipGroup>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- RecyclerView de propuestas -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_proposals"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:padding="16dp" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_proposal"
            android:tint="@color/text_secondary"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay propuestas"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Envía propuestas a guías desde la pestaña 'Buscar Guías'"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:textAlignment="center"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</LinearLayout>
