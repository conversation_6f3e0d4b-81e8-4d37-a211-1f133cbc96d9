<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <!-- AppBarLayout -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Editar Usuario"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="?attr/homeAsUpIndicator"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:paddingBottom="100dp">

            <!-- Sección: Datos Editables -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_edit"
                            android:tint="#4CAF50"
                            android:layout_marginEnd="12dp"/>

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Datos Editables"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Email (solo lectura) -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Correo Electrónico"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_email"
                        app:startIconTint="#9E9E9E"
                        app:endIconMode="custom"
                        app:endIconDrawable="@drawable/ic_password"
                        app:endIconTint="#9E9E9E"
                        app:boxStrokeColor="#9E9E9E"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress"
                            android:enabled="false"
                            android:textColor="#9E9E9E"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Nombres -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_first_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Nombres *"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_user_form"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_first_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName"
                            android:maxLength="40"
                            android:textSize="16sp"
                            android:textColor="#000000"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Apellidos -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_last_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Apellidos *"
                        android:layout_marginBottom="16dp"
                        app:startIconDrawable="@drawable/ic_user_form"
                        app:startIconTint="@color/primary"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_last_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName"
                            android:maxLength="40"
                            android:textSize="16sp"
                            android:textColor="#000000"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Teléfono -->
                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/til_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Número de Teléfono"
                            app:boxStrokeColor="@color/primary"
                            app:boxStrokeWidth="2dp"
                            app:hintTextColor="@color/primary"
                            app:boxCornerRadiusTopStart="8dp"
                            app:boxCornerRadiusTopEnd="8dp"
                            app:boxCornerRadiusBottomStart="8dp"
                            app:boxCornerRadiusBottomEnd="8dp"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_phone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="phone"
                                android:maxLength="11"
                                android:maxLines="1"
                                android:textSize="16sp"
                                android:textColor="#000000"
                                android:gravity="start|center_vertical"
                                android:paddingStart="110dp"/>
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.hbb20.CountryCodePicker
                            android:id="@+id/ccp_phone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="start|center_vertical"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="8dp"
                            app:ccp_defaultNameCode="PE"
                            app:ccp_showFlag="true"
                            app:ccp_showNameCode="false"
                            app:ccp_showFullName="false"
                            app:ccp_showPhoneCode="true"
                            app:ccp_contentColor="#000000"
                            app:ccp_textSize="16sp"
                            app:ccp_arrowSize="12dp"
                            app:ccp_customMasterCountries="PE,ES,MX,AR,CO,CL,EC,BO,VE,UY,PY,BR" />
                    </FrameLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Idiomas (Solo para Guías) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/layout_languages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_language"
                            android:tint="#FF9800"
                            android:layout_marginEnd="12dp"/>

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Idiomas"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Texto descriptivo -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Selecciona los idiomas que domina"
                        android:textColor="#666666"
                        android:textSize="16sp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Buscador de idiomas -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/Widget.Material3.TextInputLayout.FilledBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="Busca un idioma"
                        app:boxBackgroundColor="#F8F8F8"
                        app:boxCornerRadiusBottomEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusTopStart="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_search"
                        app:startIconTint="#8E8E93"
                        app:hintTextColor="#8E8E93">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_search_language"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textColor="#000000"
                            android:textSize="16sp"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Chips de idiomas seleccionados -->
                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_selected_languages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        app:chipSpacing="8dp"
                        app:singleLine="false"/>

                    <!-- Divisor -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E0E0E0"
                        android:layout_marginBottom="16dp"/>

                    <!-- Lista de idiomas disponibles -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_available_languages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:scrollbars="vertical"
                        android:clipToPadding="false"
                        android:paddingBottom="8dp"
                        android:nestedScrollingEnabled="false"/>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección: Información de Empresa (Solo para COMPANY_ADMIN, solo lectura) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/layout_company_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                android:layout_marginHorizontal="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_company"
                            android:tint="#9C27B0"
                            android:layout_marginEnd="12dp"/>

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Información de la Empresa"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"/>

                    <!-- Razón Social -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Razón Social:"
                            android:textColor="#757575"
                            android:textSize="14sp"/>

                        <TextView
                            android:id="@+id/tv_business_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-"
                            android:textColor="#2C2C2C"
                            android:textSize="14sp"
                            android:textStyle="bold"/>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#F5F5F5"
                        android:layout_marginVertical="4dp"/>

                    <!-- RUC -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="RUC:"
                            android:textColor="#757575"
                            android:textSize="14sp"/>

                        <TextView
                            android:id="@+id/tv_ruc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-"
                            android:textColor="#2C2C2C"
                            android:textSize="14sp"
                            android:textStyle="bold"/>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#F5F5F5"
                        android:layout_marginVertical="4dp"/>

                    <!-- Nombre Comercial -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Nombre Comercial:"
                            android:textColor="#757575"
                            android:textSize="14sp"/>

                        <TextView
                            android:id="@+id/tv_commercial_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-"
                            android:textColor="#2C2C2C"
                            android:textSize="14sp"
                            android:textStyle="bold"/>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#F5F5F5"
                        android:layout_marginVertical="4dp"/>

                    <!-- Tipo de Persona Jurídica -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingVertical="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Tipo:"
                            android:textColor="#757575"
                            android:textSize="14sp"/>

                        <TextView
                            android:id="@+id/tv_business_type"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="-"
                            android:textColor="#2C2C2C"
                            android:textSize="14sp"
                            android:textStyle="bold"/>
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Botón Guardar Cambios (FAB) -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fab_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:text="Guardar Cambios"
        app:icon="@drawable/ic_check_circle"
        app:backgroundTint="@color/primary"
        app:iconTint="@color/white"
        android:textColor="@color/white"
        app:elevation="8dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
