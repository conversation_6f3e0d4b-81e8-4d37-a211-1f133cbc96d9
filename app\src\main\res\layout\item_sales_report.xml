<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Ranking Number -->
        <TextView
            android:id="@+id/tv_ranking"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:text="1"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:background="@drawable/circle_primary"
            android:gravity="center"
            android:layout_marginEnd="16dp" />

        <!-- Service/Tour Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_item_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="City Tour Lima Centro"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_item_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12 ventas • 4.8★ • S/. 85 promedio"
                android:textSize="14sp"
                android:textColor="@color/gray"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Sales Amount -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end">

            <TextView
                android:id="@+id/tv_sales_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="S/. 1,020"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/green" />

            <TextView
                android:id="@+id/tv_growth_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="↑ 15%"
                android:textSize="12sp"
                android:textColor="@color/green"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
