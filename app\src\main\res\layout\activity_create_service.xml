<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_gray">

    <!-- AppBarLayout (como CreateTour) -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="Crear Servicio"
            app:titleTextAppearance="@style/Toolbar.TitleText2"
            app:titleCentered="true"
            app:titleTextColor="@color/white"
            app:navigationIcon="@drawable/ic_back"
            app:navigationIconTint="@color/white"
            app:layout_scrollFlags="scroll|enterAlways" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:clipToPadding="false"
        android:paddingBottom="22dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header informativo  -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@color/primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_services"
                        android:tint="@color/white"
                        android:contentDescription="Servicio" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Crear Nuevo Servicio"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Agrega imágenes, descripción y precio"
                            android:textSize="14sp"
                            android:textColor="#E8E8E8"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Form container (MISMO ID) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_service_form"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#ECECEC">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- ===== Sección: Imágenes ===== -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_camera"
                            android:tint="@color/primary"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Imágenes" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Imágenes del Servicio"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="18dp">

                        <!-- Imagen 1 (MISMO ID) -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_service_image1"
                            android:layout_width="0dp"
                            android:layout_height="130dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:strokeWidth="1dp"
                            app:strokeColor="#ECECEC"
                            android:clickable="true"
                            android:focusable="true">

                            <FrameLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/light_gray">

                                <ImageView
                                    android:id="@+id/iv_service_image1"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:visibility="gone" />

                                <LinearLayout
                                    android:id="@+id/placeholder_image1"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="vertical"
                                    android:gravity="center">

                                    <ImageView
                                        android:layout_width="44dp"
                                        android:layout_height="44dp"
                                        android:src="@drawable/ic_camera"
                                        android:tint="#9E9E9E" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Imagen 1"
                                        android:textSize="12sp"
                                        android:textColor="#757575"
                                        android:layout_marginTop="8dp" />
                                </LinearLayout>

                            </FrameLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <!-- Imagen 2 (MISMO ID) -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_service_image2"
                            android:layout_width="0dp"
                            android:layout_height="130dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:strokeWidth="1dp"
                            app:strokeColor="#ECECEC"
                            android:clickable="true"
                            android:focusable="true">

                            <FrameLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/light_gray">

                                <ImageView
                                    android:id="@+id/iv_service_image2"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:scaleType="centerCrop"
                                    android:visibility="gone" />

                                <LinearLayout
                                    android:id="@+id/placeholder_image2"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="vertical"
                                    android:gravity="center">

                                    <ImageView
                                        android:layout_width="44dp"
                                        android:layout_height="44dp"
                                        android:src="@drawable/ic_camera"
                                        android:tint="#9E9E9E" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Imagen 2"
                                        android:textSize="12sp"
                                        android:textColor="#757575"
                                        android:layout_marginTop="8dp" />
                                </LinearLayout>

                            </FrameLayout>
                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                    <!-- ===== Sección: Información ===== -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingBottom="8dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_info"
                            android:tint="@color/primary"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="Información" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Información del Servicio"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="#2C2C2C" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#EEEEEE"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_service_name"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Nombre del Servicio"
                        android:layout_marginBottom="16dp"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:startIconTint="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_service_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:textSize="16sp"
                            android:textColor="#000000" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_service_description"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Descripción del Servicio"
                        android:layout_marginBottom="16dp"
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:startIconTint="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_service_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:lines="4"
                            android:gravity="top|start"
                            android:textSize="16sp"
                            android:textColor="#000000" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_service_price"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Precio del Servicio"
                        android:layout_marginBottom="18dp"
                        app:prefixText="S/. "
                        app:boxStrokeColor="@color/primary"
                        app:hintTextColor="@color/primary"
                        app:startIconTint="@color/primary"
                        app:boxCornerRadiusTopStart="8dp"
                        app:boxCornerRadiusTopEnd="8dp"
                        app:boxCornerRadiusBottomStart="8dp"
                        app:boxCornerRadiusBottomEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_service_price"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:textSize="16sp"
                            android:textColor="#000000" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Action Buttons (MISMO IDs) -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginTop="4dp">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_cancel_service"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="56dp"
                            android:layout_weight="1"
                            android:text="@string/cancel"
                            android:textAllCaps="false"
                            android:textColor="@color/primary"
                            app:strokeColor="@color/primary"
                            app:cornerRadius="10dp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_save_service"
                            android:layout_width="0dp"
                            android:layout_height="56dp"
                            android:layout_weight="1"
                            android:layout_marginStart="12dp"
                            android:text="Guardar"
                            android:textAllCaps="false"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            app:backgroundTint="@color/primary"
                            app:cornerRadius="10dp"
                            app:icon="@drawable/ic_check_circle"
                            app:iconTint="@color/white"
                            app:iconGravity="textStart"
                            app:iconPadding="10dp" />

                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <View
                android:layout_width="match_parent"
                android:layout_height="12dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
