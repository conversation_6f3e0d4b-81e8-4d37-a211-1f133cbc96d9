<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp">

    <!-- Indicador de estado -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginEnd="16dp">

        <View
            android:id="@+id/view_status_indicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:background="@drawable/circle_primary" />

        <View
            android:layout_width="2dp"
            android:layout_height="40dp"
            android:background="@color/light_gray"
            android:layout_marginTop="4dp" />

    </LinearLayout>

    <!-- Contenido del punto -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Header con nombre y tiempo -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">

            <TextView
                android:id="@+id/tv_location_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Plaza de Armas"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="8:30 AM"
                android:textSize="14sp"
                android:textColor="@color/gray" />

        </LinearLayout>

        <!-- Descripción -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Historia del centro histórico"
            android:textSize="14sp"
            android:textColor="@color/gray" />

    </LinearLayout>

</LinearLayout>
