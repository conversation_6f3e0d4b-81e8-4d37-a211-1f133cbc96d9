plugins {
    alias(libs.plugins.android.application)
    id "com.google.android.libraries.mapsplatform.secrets-gradle-plugin"
}


android {
    namespace = 'com.example.droidtour'
    compileSdk = 34

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        applicationId "com.example.droidtour"
        minSdk 31
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // Leer API Key desde (1) variable de entorno, (2) propiedad de proyecto (p. ej. expuesta por el plugin de secrets o gradle.properties), (3) local.properties
        def localProperties = new Properties()
        def localPropertiesFile = rootProject.file('local.properties')
        if (localPropertiesFile.exists()) {
            localProperties.load(new FileInputStream(localPropertiesFile))
        }

        // Priorizar la variable de entorno (útil para CI), luego la property del proyecto (puede ser creada por el Secrets Plugin), y por último local.properties
        def mapsApiKey = System.getenv('MAPS_API_KEY') ?: project.findProperty('MAPS_API_KEY') ?: localProperties.getProperty('MAPS_API_KEY', '')

        // Inyectar en BuildConfig y resValues (asegurar comillas en resValue)
        buildConfigField "String", "MAPS_API_KEY", "\"${mapsApiKey}\""
        // Para recursos XML no incluimos comillas adicionales
        resValue "string", "maps_api_key", mapsApiKey
    }

    lint {
        abortOnError = false
        checkReleaseBuilds = false
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.google.material
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    implementation 'com.hbb20:ccp:2.7.3'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'de.hdodenhof:circleimageview:3.1.0'


    implementation platform('com.google.firebase:firebase-bom:31.4.0')
    implementation 'com.google.firebase:firebase-firestore'
    implementation 'com.google.firebase:firebase-storage'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-database'

    // Google Sign-In
    implementation 'com.google.android.gms:play-services-auth:20.7.0'

    // ZXing para generación de códigos QR
    implementation 'com.google.zxing:core:3.5.1'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'


    implementation("com.google.android.gms:play-services-maps:18.2.0")
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation("com.google.android.libraries.places:places:3.3.0")
    // SwipeRefreshLayout para pull-to-refresh
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // CameraX
    def camerax_version = "1.3.0"
    implementation "androidx.camera:camera-core:${camerax_version}"
    implementation "androidx.camera:camera-camera2:${camerax_version}"
    implementation "androidx.camera:camera-lifecycle:${camerax_version}"
    implementation "androidx.camera:camera-view:${camerax_version}"

    // ML Kit Barcode Scanning
    implementation 'com.google.mlkit:barcode-scanning:17.2.0'

    // Guava (requerido por CameraX)
    implementation 'com.google.guava:guava:31.1-android'


}

apply plugin: 'com.google.gms.google-services'
