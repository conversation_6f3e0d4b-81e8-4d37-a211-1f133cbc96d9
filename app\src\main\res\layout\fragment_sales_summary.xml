<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:scrollbars="none"
    android:padding="13dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- ================= KPI HEADER ================= -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="3dp"
            android:layout_marginHorizontal="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center">

                <!-- Ingresos -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/ic_peruvian_sol"
                        android:tint="@color/primary"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tv_total_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 12,450"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ingresos"
                        android:textSize="12sp"
                        android:textColor="#757575" />
                </LinearLayout>

                <!-- Reservas -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/ic_book_24"
                        android:tint="#4CAF50"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tv_total_bookings"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="87"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Reservas"
                        android:textSize="12sp"
                        android:textColor="#757575" />
                </LinearLayout>

                <!-- Ticket -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/ic_receipt_24"
                        android:tint="#FF9800"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tv_avg_ticket"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 143"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ticket prom."
                        android:textSize="12sp"
                        android:textColor="#757575" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>
        <!-- ================= KPI OPERATIVOS ================= -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="14dp"
            app:cardElevation="3dp"
            android:layout_marginHorizontal="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center">

                <!-- Total Tours -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/ic_map_location"
                        android:tint="@color/primary"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tv_total_tours"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="12"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tours"
                        android:textSize="12sp"
                        android:textColor="#757575" />
                </LinearLayout>

                <!-- Rating Promedio -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/ic_star"
                        android:tint="#FFC107"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tv_avg_rating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.8"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Rating"
                        android:textSize="12sp"
                        android:textColor="#757575" />
                </LinearLayout>

                <!-- Pagos a Guías -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/ic_payment"
                        android:tint="#4CAF50"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tv_guide_payments"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 3,200"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Guías"
                        android:textSize="12sp"
                        android:textColor="#757575" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>


        <!-- ================= TOP TOURS ================= -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="14dp"
            app:cardElevation="3dp"
            android:layout_marginHorizontal="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingBottom="8dp">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/ic_tour_24"
                        android:tint="@color/primary"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tours más vendidos"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#EEEEEE"
                    android:layout_marginBottom="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_top_tours"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- ================= TENDENCIA ================= -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="260dp"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="14dp"
            app:cardElevation="3dp"
            android:layout_marginHorizontal="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tendencia de ingresos"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C"
                    android:layout_marginBottom="12dp" />

                <!-- Placeholder gráfico -->
                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/line_chart_trend"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- ================= DESGLOSE FINANCIERO ================= -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="14dp"
            app:cardElevation="3dp"
            android:layout_marginHorizontal="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Desglose financiero"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C"
                    android:layout_marginBottom="16dp" />

                <!-- Bruto -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Ingresos brutos"
                        android:textSize="15sp"
                        android:textColor="#757575" />

                    <TextView
                        android:id="@+id/tv_gross_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 12,450"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary" />
                </LinearLayout>

                <!-- Comisión -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Comisión plataforma"
                        android:textSize="15sp"
                        android:textColor="#757575" />

                    <TextView
                        android:id="@+id/tv_platform_fee"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="- S/ 1,245"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:textColor="#F44336" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#EEEEEE"
                    android:layout_marginVertical="12dp" />

                <!-- Neto -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Ingresos netos"
                        android:textSize="17sp"
                        android:textStyle="bold"
                        android:textColor="#2C2C2C" />

                    <TextView
                        android:id="@+id/tv_net_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 11,205"
                        android:textSize="17sp"
                        android:textStyle="bold"
                        android:textColor="#4CAF50" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>
