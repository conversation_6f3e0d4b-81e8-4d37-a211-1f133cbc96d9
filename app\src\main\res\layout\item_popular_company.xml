<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_company"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    app:strokeWidth="1dp"
    app:strokeColor="#E0E0E0"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white">

        <!-- Header con logo y nombre -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <!-- Logo de la empresa -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="72dp"
                android:layout_height="72dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                app:strokeWidth="2dp"
                app:strokeColor="#E0E0E0">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#FAFAFA">

                    <ImageView
                        android:id="@+id/iv_company_logo"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerInside"
                        android:padding="12dp"
                        android:contentDescription="Logo empresa"
                        tools:src="@drawable/ic_company" />

                    <!-- Placeholder inicial -->
                    <TextView
                        android:id="@+id/tv_company_initial"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:visibility="gone"
                        tools:text="LA"
                        tools:visibility="visible" />
                </FrameLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Info de la empresa -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lima Adventure Tours"
                    android:textSize="17sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C"
                    android:maxLines="2"
                    android:ellipsize="end"
                    tools:text="Lima Adventure Tours" />

                <!-- Badge verificado (opcional) -->
                <LinearLayout
                    android:id="@+id/layout_verified"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    android:layout_marginTop="4dp"
                    tools:visibility="visible">

                    <ImageView
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:src="@drawable/ic_verified"
                        android:tint="#4CAF50"
                        android:contentDescription="Verificado" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Empresa Verificada"
                        android:textSize="11sp"
                        android:textColor="#4CAF50"
                        android:layout_marginStart="4dp" />
                </LinearLayout>

                <!-- Ubicación -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="6dp">

                    <ImageView
                        android:layout_width="14dp"
                        android:layout_height="14dp"
                        android:src="@drawable/ic_location"
                        android:tint="#757575"
                        android:contentDescription="Ubicación" />

                    <TextView
                        android:id="@+id/tv_company_location"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Lima, Perú"
                        android:textSize="13sp"
                        android:textColor="#757575"
                        android:layout_marginStart="4dp"
                        tools:text="Lima, Perú" />
                </LinearLayout>
            </LinearLayout>

            <!-- Botón de favorito -->
            <ImageButton
                android:id="@+id/btn_favorite"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_favorite"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:tint="#E91E63"
                android:contentDescription="Favorito" />
        </LinearLayout>

        <!-- Divisor -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginHorizontal="16dp" />

        <!-- Stats -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp">

            <!-- Rating -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_star"
                        android:tint="#FF9800"
                        android:contentDescription="Rating" />

                    <TextView
                        android:id="@+id/tv_rating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.8"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#FF9800"
                        android:layout_marginStart="4dp"
                        tools:text="4.8" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_reviews_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="245 reseñas"
                    android:textSize="11sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="2dp"
                    tools:text="245 reseñas" />
            </LinearLayout>

            <!-- Divisor vertical -->
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#EEEEEE"
                android:layout_marginHorizontal="16dp" />

            <!-- Tours count -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_tours_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#2196F3"
                    tools:text="12" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tours"
                    android:textSize="11sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="2dp" />
            </LinearLayout>

            <!-- Divisor vertical -->
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#EEEEEE"
                android:layout_marginHorizontal="16dp" />

            <!-- Experiencia -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_experience_years"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5+"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#4CAF50"
                    tools:text="5+" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Años"
                    android:textSize="11sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="2dp" />
            </LinearLayout>
        </LinearLayout>

        <!-- Divisor -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#EEEEEE"
            android:layout_marginHorizontal="16dp" />

        <!-- Servicios destacados (chips) -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_services"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="12dp"
            app:singleLine="true"
            app:chipSpacingHorizontal="6dp">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_service_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Guía profesional"
                android:textSize="11sp"
                style="@style/Widget.MaterialComponents.Chip.Action"
                app:chipBackgroundColor="#E3F2FD"
                app:chipStrokeWidth="0dp"
                android:textColor="#1976D2"
                app:chipMinHeight="28dp"
                app:chipIconTint="#1976D2"
                app:chipIconSize="14dp"
                tools:text="Guía profesional" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_service_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Transporte"
                android:textSize="11sp"
                style="@style/Widget.MaterialComponents.Chip.Action"
                app:chipBackgroundColor="#E8F5E9"
                app:chipStrokeWidth="0dp"
                android:textColor="#388E3C"
                app:chipMinHeight="28dp"
                app:chipIconTint="#388E3C"
                app:chipIconSize="14dp"
                tools:text="Transporte" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_service_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Comidas"
                android:textSize="11sp"
                style="@style/Widget.MaterialComponents.Chip.Action"
                app:chipBackgroundColor="#FFF3E0"
                app:chipStrokeWidth="0dp"
                android:textColor="#F57C00"
                app:chipMinHeight="28dp"
                app:chipIconTint="#F57C00"
                app:chipIconSize="14dp"
                tools:text="Comidas" />
        </com.google.android.material.chip.ChipGroup>

        <!-- Botones de acción -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:paddingTop="12dp"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_company"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:text="Ver Perfil"
                android:textSize="14sp"
                app:iconTint="@color/primary"
                app:iconSize="18dp"
                app:iconGravity="textStart"
                app:cornerRadius="8dp"
                app:strokeColor="@color/primary"
                android:textColor="@color/primary"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_tours"
                style="@style/Widget.Material3.Button"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:text="Ver Tours"
                android:textSize="14sp"
                app:icon="@drawable/ic_map_location"
                app:iconSize="18dp"
                app:iconGravity="textStart"
                app:cornerRadius="8dp"
                app:backgroundTint="@color/primary" />
        </LinearLayout>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>