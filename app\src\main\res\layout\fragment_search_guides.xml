<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- Header con título y contador -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Guías Disponibles"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tv_guides_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0 guías encontrados"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Botón de minimizar filtros -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_toggle_filters"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Filtros"
            app:icon="@drawable/ic_expand_less"
            app:iconTint="@color/primary"
            android:textColor="@color/primary"
            app:iconGravity="textStart"
            style="@style/Widget.Material3.Button.TextButton"
            android:textSize="13sp"
            android:layout_marginEnd="8dp" />

        <!-- Botón de ordenar -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_sort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ordenar"
            app:icon="@drawable/ic_sort"
            app:iconGravity="textStart"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:textSize="13sp" />

    </LinearLayout>

    <!-- Filtros Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_filters"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:cardElevation="2dp"
        app:cardCornerRadius="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@color/white">

            <!-- Filtro por Rating -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_star"
                    android:tint="@color/warning" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Calificación mínima"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="8dp" />

                <TextView
                    android:id="@+id/tv_rating_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0.0 ⭐"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/warning" />

            </LinearLayout>

            <com.google.android.material.slider.Slider
                android:id="@+id/slider_rating"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:valueFrom="0.0"
                android:valueTo="5.0"
                android:stepSize="0.5"
                android:value="0.5"
                app:labelBehavior="gone"
                app:thumbColor="@color/primary"
                app:trackColorInactive="@color/primary_light"
                app:trackColorActive="@color/primary"

                />

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider"
                android:layout_marginVertical="12dp" />

            <!-- Filtro de Idiomas -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_language"
                    android:tint="@color/primary" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Idiomas requeridos"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_clear_languages"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Limpiar"
                    android:textColor="@color/primary"
                    android:textSize="12sp"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:minWidth="0dp"
                    android:minHeight="0dp"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp" />

            </LinearLayout>

            <!-- ChipGroup con wrap automático -->
            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_languages"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:chipSpacingHorizontal="8dp"
                app:chipSpacingVertical="4dp">

                <!-- Los chips se agregarán dinámicamente -->

            </com.google.android.material.chip.ChipGroup>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- RecyclerView de guías -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_guides"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false"
        android:clipToPadding="false"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp"
        android:visibility="visible" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">



                <ImageView
                    android:layout_width="140dp"
                    android:layout_height="140dp"
                    android:src="@drawable/ic_person"
                    android:tint="@color/text_secondary"
                    android:alpha="0.4" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No hay guías disponibles"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginTop="24dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Los guías disponibles aparecerán aquí una vez que sean aprobados por el administrador."
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:textAlignment="center"
                    android:layout_marginTop="12dp" />

                <!-- Sugerencias -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:cardElevation="2dp"
                    app:cardCornerRadius="12dp"
                    app:cardBackgroundColor="@color/primary_light">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="💡 Sugerencias"
                            android:textSize="15sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="• Ajusta el filtro de calificación mínima\n• Prueba con diferentes combinaciones de idiomas\n• Los guías con tours activos no aparecerán aquí"
                            android:textSize="13sp"
                            android:textColor="@color/text_secondary"
                            android:layout_marginTop="8dp"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</LinearLayout>

</androidx.core.widget.NestedScrollView>
