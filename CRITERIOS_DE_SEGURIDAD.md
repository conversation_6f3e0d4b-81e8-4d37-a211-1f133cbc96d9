# 🔐 Criterios de Seguridad - Proyecto IOT

## Resumen de Seguridad

Este proyecto implementa múltiples capas de seguridad para proteger los datos de los usuarios, las transacciones y la integridad del sistema. Este documento detalla cada criterio de seguridad aplicado y su ubicación en el código.

---

## 🛡️ 1. Autenticación Segura

### 1.1 Firebase Authentication

**Descripción:** Utilizamos Firebase Authentication para gestionar la identidad de usuarios, delegando la seguridad de credenciales a Google.

**Ubicación:** `app/src/main/java/com/example/droidtour/firebase/FirebaseAuthManager.java`

**Características:**
- ✅ Contraseñas hasheadas automáticamente por Firebase (bcrypt)
- ✅ Tokens JWT renovados automáticamente
- ✅ Sesiones seguras con expiración
- ✅ Protección contra ataques de fuerza bruta

```java
// FirebaseAuthManager.java
public void loginWithEmail(String email, String password, AuthCallback callback) {
    mAuth.signInWithEmailAndPassword(email, password)
        .addOnCompleteListener(task -> {
            if (task.isSuccessful()) callback.onSuccess(mAuth.getCurrentUser());
            else callback.onFailure(task.getException());
        });
}
```

### 1.2 Validación de Roles Multi-nivel

**Descripción:** Sistema robusto de verificación de roles y estados de usuario.

**Ubicación:** `app/src/main/java/com/example/droidtour/LoginActivity.java`

**Beneficios:**
- ✅ Verificación de tipo de usuario (CLIENT, GUIDE, ADMIN, SUPERADMIN)
- ✅ Validación de estado de aprobación para guías
- ✅ Control de acceso basado en roles

```java
// Validación de rol y estado al iniciar sesión
if ("GUIDE".equals(userType)) {
    checkGuideApprovalStatus(uid);
} else {
    redirigirSegunRol();
}
```

---

## 🔒 2. Protección de Datos

### 2.1 Reglas de Seguridad de Firebase Realtime Database

**Descripción:** Control de acceso granular a nivel de base de datos.

**Ubicación:** `database-rules.json`

**Reglas implementadas:**
```javascript
{
  "rules": {
    "conversations": {
      "$conversationId": {
        ".read": "auth != null && (
          data.child('clientId').val() == auth.uid ||
          data.child('companyId').val() == auth.uid ||
          root.child('users').child(auth.uid).child('companyId').val() == data.child('companyId').val()
        )",
        ".write": "auth != null && (
          data.child('clientId').val() == auth.uid ||
          root.child('users').child(auth.uid).child('companyId').val() == data.child('companyId').val()
        )"
      }
    },
    "users": {
      "$userId": {
        ".read": "auth != null",
        ".write": "auth != null && $userId == auth.uid"
      }
    }
  }
}
```

### 2.2 Enmascaramiento de Datos Sensibles

**Descripción:** Los datos sensibles se ocultan en la UI para proteger información financiera.

**Ubicación:** `app/src/main/java/com/example/droidtour/models/PaymentMethod.java`

```java
// Enmascarar número de tarjeta - solo mostrar últimos 4 dígitos
private String maskCardNumber(String fullCardNumber) {
    if (fullCardNumber == null || fullCardNumber.length() < 4) {
        return "****";
    }
    return "****" + fullCardNumber.substring(fullCardNumber.length() - 4);
}
```

### 2.3 Protección de CVV

**Descripción:** El CVV no se almacena permanentemente por seguridad.

**Ubicación:** `app/src/main/java/com/example/droidtour/models/PaymentMethod.java`

```java
public Map<String, Object> toMap() {
    Map<String, Object> map = new HashMap<>();
    // ... otros campos
    // NO incluir CVV por seguridad
    return map;
}
```

---

## 🔑 3. Gestión de Sesiones

### 3.1 Persistencia Segura de Sesión

**Descripción:** La sesión del usuario se mantiene de forma segura usando SharedPreferences.

**Ubicación:** `app/src/main/java/com/example/droidtour/utils/PreferencesManager.java`

```java
// Almacenamiento seguro de datos de usuario
public void saveUserData(String userId, String name, String email, String phone, String userType) {
    editor.putString(KEY_USER_ID, userId);
    editor.putString(KEY_USER_NAME, name);
    editor.putString(KEY_USER_EMAIL, email);
    editor.putString(KEY_USER_PHONE, phone);
    editor.putString(KEY_USER_TYPE, userType);
    editor.putBoolean(KEY_IS_LOGGED_IN, true);
    editor.apply();
}
```

### 3.2 Verificación de Sesión Activa

**Descripción:** Validar que el usuario esté autenticado antes de operaciones críticas.

**Ubicación:** `app/src/main/java/com/example/droidtour/GuideTrackingActivity.java`

```java
// Validar sesión PRIMERO
if (!prefsManager.isLoggedIn()) {
    redirectToLogin();
    finish();
    return;
}

// Validar que el usuario sea ADMIN o GUIDE
String userType = prefsManager.getUserType();
if (userType == null || (!userType.equals("ADMIN") && !userType.equals("COMPANY_ADMIN") && !userType.equals("GUIDE"))) {
    redirectToLogin();
    finish();
    return;
}
```

### 3.3 Cierre de Sesión Seguro

**Descripción:** Limpieza completa de datos al cerrar sesión.

**Ubicación:** `app/src/main/java/com/example/droidtour/utils/PreferencesManager.java`

```java
public void logout() {
    editor.clear();
    editor.apply();
}

public void clearUserData() {
    editor.remove(KEY_USER_ID);
    editor.remove(KEY_USER_NAME);
    editor.remove(KEY_USER_EMAIL);
    // ... limpiar todos los datos sensibles
    editor.apply();
}
```

---

## 🚫 4. Control de Acceso

### 4.1 Verificación de Estado de Guías

**Descripción:** Los guías turísticos requieren aprobación antes de operar.

**Ubicación:** `app/src/main/java/com/example/droidtour/LoginActivity.java`

```java
private void checkGuideApprovalStatus(String userId) {
    firestoreManager.getUserRoles(userId, new FirestoreManager.FirestoreCallback() {
        @Override
        public void onSuccess(Object result) {
            String guideStatus = extractGuideStatus(rolesData);
            
            if ("active".equals(guideStatus)) {
                // Guía aprobado
                redirigirSegunRol();
            } else {
                // Guía no aprobado
                redirectToApprovalPending();
            }
        }
    });
}
```

### 4.2 Validación de Tipos de Usuario

**Descripción:** Validación estricta de tipos de usuario permitidos.

**Ubicación:** `app/src/main/java/com/example/droidtour/firebase/FirestoreManager.java`

```java
private String validateUser(User user) {
    String t = user.getUserType().trim().toUpperCase();
    if (!t.equals("CLIENT") && !t.equals("GUIDE") && !t.equals("COMPANY_ADMIN") && !t.equals("SUPERADMIN")) {
        return "Invalid userType. Use CLIENT, GUIDE, COMPANY_ADMIN or SUPERADMIN";
    }
    return null;
}
```

---

## 🛑 5. Validación de Entrada

### 5.1 Validación de Tarjetas de Crédito

**Descripción:** Implementación del algoritmo de Luhn para validar números de tarjeta.

**Ubicación:** `app/src/main/java/com/example/droidtour/client/AddPaymentMethodActivity.java`

```java
private boolean isValidLuhn(String cardNumber) {
    int sum = 0;
    boolean alternate = false;
    
    for (int i = cardNumber.length() - 1; i >= 0; i--) {
        int digit = Character.getNumericValue(cardNumber.charAt(i));
        
        if (alternate) {
            digit *= 2;
            if (digit > 9) digit -= 9;
        }
        
        sum += digit;
        alternate = !alternate;
    }
    
    return (sum % 10 == 0);
}
```

### 5.2 Validación de Formularios de Registro

**Descripción:** Validación exhaustiva de datos de entrada en formularios.

**Ubicación:** `app/src/main/java/com/example/droidtour/client/ClientRegistrationActivity.java`

```java
// Validar DNI: solo números, exactamente 8 cifras
if ("DNI".equals(tipoDocumento)) {
    String numeroSinEspacios = numeroDocumento.replaceAll("\\s+", "");
    if (!numeroSinEspacios.matches("\\d{8}")) {
        etNumeroDocumento.setError("El DNI debe tener exactamente 8 dígitos numéricos");
        return false;
    }
}
```

### 5.3 Validación de Números de Teléfono

**Ubicación:** `app/src/main/java/com/example/droidtour/client/ClientEditProfileActivity.java`

```java
private boolean validateFields() {
    String localNumber = etPhoneNumber.getText().toString().trim().replaceAll("\\s+", "");

    if (!localNumber.isEmpty()) {
        // Validar que el número tenga exactamente 9 dígitos
        if (localNumber.length() != 9) {
            etPhoneNumber.setError("El número de teléfono debe tener 9 dígitos");
            return false;
        }

        // Validar que solo contenga dígitos
        if (!localNumber.matches("\\d{9}")) {
            etPhoneNumber.setError("El número de teléfono solo debe contener dígitos");
            return false;
        }
    }
    return true;
}
```

### 5.4 Validación de Datos Empresariales

**Ubicación:** `app/src/main/java/com/example/droidtour/superadmin/AdminRegistrationActivity.java`

```java
private boolean validateForm() {
    // Validar RUC
    if (TextUtils.isEmpty(etRuc.getText()) || etRuc.getText().length() != 11) {
        etRuc.setError("RUC debe tener 11 dígitos");
        return false;
    }

    // Validar longitud de nombres
    String firstName = etAdminFirstName.getText().toString().trim();
    if (firstName.length() > 40) {
        etAdminFirstName.setError("Los nombres no pueden exceder 40 caracteres");
        return false;
    }

    return true;
}
```

---

## 📱 6. Permisos de Android

### 6.1 Permisos Declarados

**Ubicación:** `app/src/main/AndroidManifest.xml`

```xml
<!-- Solo los permisos necesarios -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
<uses-permission android:name="android.permission.VIBRATE"/>

<!-- Cámara no es requerida obligatoriamente -->
<uses-feature android:name="android.hardware.camera" android:required="false" />
```

### 6.2 Activities No Exportadas

**Descripción:** Todas las activities internas están protegidas contra acceso externo.

**Ubicación:** `app/src/main/AndroidManifest.xml`

```xml
<!-- Todas las activities internas tienen exported="false" -->
<activity android:name=".superadmin.SuperadminMainActivity" android:exported="false" />
<activity android:name=".TourAdminMainActivity" android:exported="false" />
<activity android:name=".client.ClientMainActivity" android:exported="false" />

<!-- Solo la activity de entrada tiene exported="true" -->
<activity android:name=".welcome.WelcomeActivity" android:exported="true">
    <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
    </intent-filter>
</activity>
```

### 6.3 FileProvider Seguro

**Descripción:** Configuración segura para compartir archivos.

**Ubicación:** `app/src/main/AndroidManifest.xml`

```xml
<!-- FileProvider para compartir archivos PDF -->
<provider
    android:name="androidx.core.content.FileProvider"
    android:authorities="${applicationId}.fileprovider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
</provider>
```

---

## 🔐 7. Protección de API Keys

### 7.1 Almacenamiento Seguro de Keys

**Descripción:** Uso del Secrets Gradle Plugin para proteger API keys.

**Ubicación:** `app/build.gradle`

```groovy
// Leer API Key de forma segura
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localProperties.load(new FileInputStream(localPropertiesFile))
}

// Priorizar variable de entorno, luego property del proyecto, y por último local.properties
def mapsApiKey = System.getenv('MAPS_API_KEY') ?:
                 project.findProperty('MAPS_API_KEY') ?:
                 localProperties.getProperty('MAPS_API_KEY', '')

buildConfigField "String", "MAPS_API_KEY", "\"${mapsApiKey}\""
resValue "string", "maps_api_key", mapsApiKey
```

### 7.2 Configuración del Secrets Plugin

**Ubicación:** `settings.gradle`

```groovy
plugins {
    // Secrets Gradle Plugin para gestionar claves en equipos y CI
    id("com.google.android.libraries.mapsplatform.secrets-gradle-plugin") version "2.0.1"
}
```

### 7.3 Template de Configuración

**Ubicación:** `app/secrets.properties.template`

```properties
# Buenas prácticas
- Restringe la clave en Google Cloud Console (paquete Android + SHA-1) y por APIs.
- Nunca comitees `app/secrets.properties` ni `local.properties`.
- Considera rotar la clave si alguien la filtra.

MAPS_API_KEY=YOUR_MAPS_API_KEY_HERE
```

---

## 🔄 8. Gestión de Cache y Archivos

### 8.1 Cache Seguro con Timestamp

**Descripción:** Sistema de cache con validación temporal.

**Ubicación:** `app/src/main/java/com/example/droidtour/managers/FileManager.java`

```java
public boolean guardarCache(String clave, String datos) {
    try {
        JSONObject cacheData = new JSONObject();
        cacheData.put("timestamp", System.currentTimeMillis());
        cacheData.put("data", datos);
        return guardarJSON("cache_" + clave + ".json", cacheData);
    } catch (JSONException e) {
        Log.e(TAG, "Error creando cache: " + e.getMessage());
        return false;
    }
}
```

---

## 📊 Resumen de Controles de Seguridad

| Área | Control | Estado |
|------|---------|--------|
| Autenticación | Firebase Auth | ✅ Implementado |
| Autenticación | Validación de roles | ✅ Implementado |
| Autorización | Reglas Firebase | ✅ Implementado |
| Autorización | Control de acceso por estado | ✅ Implementado |
| Datos | Enmascaramiento de tarjetas | ✅ Implementado |
| Datos | Protección de CVV | ✅ Implementado |
| Datos | Encriptación en tránsito | ✅ Automático (Firebase) |
| Sesiones | Persistencia segura | ✅ Implementado |
| Sesiones | Cierre de sesión limpio | ✅ Implementado |
| Sesiones | Verificación de sesión activa | ✅ Implementado |
| Entrada | Validación de formularios | ✅ Implementado |
| Entrada | Algoritmo de Luhn | ✅ Implementado |
| Entrada | Validación de DNI/RUC | ✅ Implementado |
| Permisos | Runtime permissions | ✅ Implementado |
| Permisos | Activities no exportadas | ✅ Implementado |
| API Keys | Secrets Gradle Plugin | ✅ Implementado |
| API Keys | Variables de entorno | ✅ Implementado |
| Cache | Timestamp validation | ✅ Implementado |

---

## 🚨 Recomendaciones Adicionales para Producción

### Seguridad de Red
1. **Implementar Network Security Config** para forzar HTTPS
2. **Certificate Pinning** para mayor seguridad en comunicaciones
3. **Configurar Content Security Policy** en WebViews si se usan

### Ofuscación y Protección de Código
1. **Habilitar ProGuard/R8** para ofuscar el código en release builds
2. **Configurar reglas de ofuscación** específicas para Firebase y librerías
3. **Implementar detección de root/jailbreak** para dispositivos comprometidos

### Monitoreo y Auditoría
1. **Firebase Crashlytics** para detectar anomalías y crashes
2. **Firebase Performance Monitoring** para detectar comportamientos sospechosos
3. **Logging seguro** sin exponer datos sensibles
4. **Auditoría de seguridad** antes del lanzamiento

### Configuración de Firebase
1. **Configurar reglas de Firestore** más granulares para producción
2. **Habilitar App Check** para prevenir accesos no autorizados
3. **Implementar rate limiting** en Cloud Functions
4. **Configurar alertas** para actividades sospechosas

### Gestión de Datos
1. **Implementar backup cifrado** de datos críticos
2. **Política de retención de datos** según normativas
3. **Anonimización de datos** para analytics
4. **Cumplimiento GDPR/CCPA** si aplica

---

*Documento de seguridad - Proyecto IOT*
*Última actualización: Diciembre 2024*
*Versión: 1.0*
```
