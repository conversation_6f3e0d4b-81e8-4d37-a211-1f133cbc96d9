<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header con foto y nombre del guía -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="?attr/selectableItemBackground"
            android:paddingVertical="4dp">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/img_guide"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_person"
                app:strokeWidth="2dp"
                app:strokeColor="@color/primary_light"
                app:shapeAppearanceOverlay="@style/CircleImageView" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="12dp">

                <TextView
                    android:id="@+id/tv_guide_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Nombre del Guía"
                    android:textSize="17sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fecha"
                    android:textSize="13sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <!-- Botón info guía -->
            <ImageButton
                android:id="@+id/btn_show_guide_details"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_info"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:tint="@color/primary"
                android:contentDescription="Ver detalles del guía" />

            <!-- Badge de estado con color -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pendiente"
                android:textSize="11sp"
                android:textStyle="bold"
                app:chipMinHeight="28dp"
                app:ensureMinTouchTargetSize="false"
                style="@style/Widget.Material3.Chip.Assist" />

        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp" />

        <!-- Información del tour en tarjeta destacada -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/primary_light"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_tour"
                        android:tint="@color/primary" />

                    <TextView
                        android:id="@+id/tv_tour_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Nombre del Tour"
                        android:textSize="15sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginStart="8dp"
                        android:maxLines="2"
                        android:ellipsize="end" />

                </LinearLayout>

                <!-- Monto de pago -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="8dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_money"
                        android:tint="@color/success" />

                    <TextView
                        android:id="@+id/tv_payment"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="S/ 200.00"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/success"
                        android:layout_marginStart="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Pago propuesto"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Estado con diseño mejorado -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="12dp"
            android:padding="8dp"
            android:background="@drawable/rounded_background_light">

            <ImageView
                android:id="@+id/ic_status"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_status"
                android:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Estado: "
                android:textSize="13sp"
                android:textColor="@color/text_secondary"
                android:layout_marginStart="8dp" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pendiente"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Notas (opcional) -->
        <TextView
            android:id="@+id/tv_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Notas de la propuesta"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:fontFamily="monospace"
            android:padding="12dp"
            android:background="@drawable/rounded_background_light"
            android:layout_marginTop="12dp"
            android:visibility="gone" />

        <!-- Botón cancelar (solo para pendientes) -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Cancelar Propuesta"
            android:textColor="@color/error"
            app:icon="@drawable/ic_cancel"
            app:iconTint="@color/error"
            app:strokeColor="@color/error"
            app:strokeWidth="1dp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_marginTop="12dp"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
