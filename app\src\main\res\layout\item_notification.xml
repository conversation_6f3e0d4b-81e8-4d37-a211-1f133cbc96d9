<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_notification"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:background="@color/white">

        <!-- Unread Indicator -->
        <View
            android:id="@+id/unread_indicator"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_alignParentStart="true"
            android:layout_marginTop="4dp"
            android:background="@drawable/unread_indicator"
            android:visibility="visible" />

        <!-- Icon Container -->
        <FrameLayout
            android:id="@+id/icon_container"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_toEndOf="@id/unread_indicator"
            android:layout_marginStart="12dp">

            <View
                android:id="@+id/icon_background"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/notification_icon_bg_blue" />

            <ImageView
                android:id="@+id/iv_notification_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_book_24"
                android:tint="#2196F3" />
        </FrameLayout>

        <!-- Content Container -->
        <LinearLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/icon_container"
            android:layout_toStartOf="@id/menu_button"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:orientation="vertical">

            <!-- Title and Time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_notification_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Reserva confirmada"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#2C2C2C"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tv_notification_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2h"
                    android:textSize="12sp"
                    android:textColor="#999999"
                    android:layout_marginStart="8dp" />
            </LinearLayout>

            <!-- Message -->
            <TextView
                android:id="@+id/tv_notification_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tu reserva para el tour 'Machu Picchu 2 días' ha sido confirmada para el 15 de Diciembre"
                android:textSize="14sp"
                android:textColor="#666666"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginTop="4dp"
                android:lineSpacingExtra="2dp" />

            <!-- Category Tag (Optional) -->
            <TextView
                android:id="@+id/tv_notification_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Reservas"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="#2196F3"
                android:background="@drawable/category_tag_bg"
                android:paddingHorizontal="10dp"
                android:paddingVertical="4dp"
                android:layout_marginTop="8dp"
                android:visibility="visible" />

        </LinearLayout>

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/menu_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_more_vert"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:tint="#999999"
            android:contentDescription="Más opciones" />

    </RelativeLayout>

</com.google.android.material.card.MaterialCardView>