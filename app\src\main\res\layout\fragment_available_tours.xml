<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- Search Bar -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/search"
        app:startIconDrawable="@android:drawable/ic_menu_search"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Available Tours List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_available_tours"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/til_search"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@android:drawable/ic_menu_mapmode"
            android:tint="@color/gray"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay tours disponibles"
            android:textSize="18sp"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
