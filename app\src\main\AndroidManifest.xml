<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32" />

    <!-- Indicar que la cámara no es requerida para que el permiso no marque error en Lint -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />

    <!-- Permisos para notificaciones -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.VIBRATE"/>

    <application
        android:name=".DroidTourApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DroidTour">


        <!-- Meta-data para Google Services -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/maps_api_key" />

        <!-- Google Sign-In Activity -->
        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- Bienvenida-->
        <activity
            android:name=".welcome.WelcomeActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".welcome.Onboarding1" android:exported="false"/>
        <activity android:name=".welcome.Onboarding2" android:exported="false"/>

        <!-- Eliminada la actividad MainActivity ya que la navegación inicia en WelcomeActivity y Login -->
        <!-- <activity android:name=".MainActivity" android:exported="true"/> -->

        <activity
            android:name=".superadmin.SuperadminMainActivity"
            android:exported="false" />

        <activity
            android:name=".TourAdminMainActivity"
            android:exported="false" />

        <activity
            android:name=".AdminMyAccountActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />
        
        <activity
            android:name=".admin.AdminProfileActivity"
            android:exported="false"
            android:parentActivityName=".AdminMyAccountActivity" />

        <activity
            android:name=".TourGuideMainActivity"
            android:exported="false" />

        <activity
            android:name=".client.ClientMainActivity"
            android:exported="false" />


        <activity
            android:name=".superadmin.SuperadminUsersActivity"
            android:exported="false"
            android:parentActivityName=".superadmin.SuperadminMainActivity" />

        <activity
            android:name=".superadmin.SuperadminReportsActivity"
            android:exported="false"
            android:parentActivityName=".superadmin.SuperadminMainActivity" />

        <activity
            android:name=".superadmin.CompaniesReportsActivity"
            android:exported="false"
            android:parentActivityName=".superadmin.SuperadminReportsActivity" />

        <activity
            android:name=".superadmin.SuperadminLogsActivity"
            android:exported="false"
            android:parentActivityName=".superadmin.SuperadminMainActivity" />

        <activity
            android:name=".superadmin.SuperadminProfileActivity"
            android:exported="false"
            android:parentActivityName=".superadmin.SuperadminMainActivity" />

        <!-- Tour Admin Activities -->
        <activity
            android:name=".admin.CompanyInfoActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".admin.CreateServiceActivity"
            android:exported="false"
            android:parentActivityName=".admin.ServiceManagementActivity" />
        
        <activity
            android:name=".admin.ServiceManagementActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".admin.CreateTourActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".admin.TourLocationsMapActivity"
            android:exported="false"
            android:parentActivityName=".admin.CreateTourActivity" />

        <activity
            android:name=".admin.TourManagementActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".GuideManagementActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".GuideTrackingActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".CheckoutAlertsActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".SalesReportsActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".CustomerChatActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".ChatDetailActivity"
            android:exported="false"
            android:parentActivityName=".CustomerChatActivity" />

        <!-- Client Activities -->
        <activity
            android:name=".CompaniesListActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />
        
        <activity
            android:name=".ToursCatalogActivity"
            android:exported="false"
            android:parentActivityName=".CompaniesListActivity" />
        
        <activity
            android:name=".TourDetailActivity"
            android:exported="false"
            android:parentActivityName=".ToursCatalogActivity" />
        
        <activity
            android:name=".MeetingPointMapActivity"
            android:exported="false"
            android:parentActivityName=".TourDetailActivity" />
        
        <activity
            android:name=".TourStopsMapActivity"
            android:exported="false"
            android:parentActivityName=".TourDetailActivity" />
        
        <activity
            android:name=".MyReservationsActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />
        
        <activity
            android:name=".client.ClientQRCodesActivity"
            android:exported="false"
            android:parentActivityName=".MyReservationsActivity" />
        
        <activity
            android:name=".client.PaymentMethodsActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />
        
        <activity
            android:name=".client.AddPaymentMethodActivity"
            android:exported="false"
            android:parentActivityName=".client.PaymentMethodsActivity" />
        
        <activity
            android:name=".client.EditPaymentMethodActivity"
            android:exported="false"
            android:parentActivityName=".client.PaymentMethodsActivity" />
        
        <activity
            android:name=".client.CompanyChatActivity"
            android:exported="false"
            android:parentActivityName=".TourDetailActivity" />
        
        <activity
            android:name=".client.ClientProfileActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />

        <activity
            android:name=".client.ClientEditProfileActivity"
            android:theme="@style/Theme.DroidTour"
            android:parentActivityName=".client.ClientProfileActivity"
            android:exported="false">

            <!-- Opcional: Configurar la relación padre-hijo para la navegación -->
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".client.ClientProfileActivity" />
        </activity>

        <activity
            android:name=".client.ClientSettingsActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />
        
        <activity
            android:name=".client.ClientMyAccount"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />

        <!-- Client Notifications Activity -->
        <activity
            android:name=".client.ClientNotificationsActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />

        <activity
            android:name=".GuideMyAccount"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".superadmin.SuperadminMyAccount"
            android:exported="false"
            android:parentActivityName=".superadmin.SuperadminMainActivity" />

        <activity
            android:name=".TourRatingActivity"
            android:exported="false"
            android:parentActivityName=".MyReservationsActivity" />
        
        <activity
            android:name=".TourBookingActivity"
            android:exported="false"
            android:parentActivityName=".TourDetailActivity" />
        
        <activity
            android:name=".AllReviewsActivity"
            android:exported="false"
            android:parentActivityName=".TourDetailActivity" />
        
        <!-- Legacy Client Activities -->
        <activity
            android:name=".client.ClientHistoryActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />
        <activity
            android:name=".client.ClientQRActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />

        <!-- Tour Guide Activities -->
        <activity
            android:name=".TourOffersActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".GuideActiveToursActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".QRScannerActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".LocationTrackingActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".GuideStopsManagementActivity"
            android:exported="false"
            android:parentActivityName=".LocationTrackingActivity" />

        <activity
            android:name=".GuideRegistrationActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".GuideProfileActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".GuideNotificationsActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".GuideActiveTourDetailActivity"
            android:exported="false"
            android:parentActivityName=".GuideActiveToursActivity" />

        <!-- Actividades de Autenticación -->
        <activity
            android:name=".LoginActivity"
            android:exported="true" />

        <activity
            android:name=".RoleSelectionActivity"
            android:exported="false"
            android:parentActivityName=".LoginActivity" />

        <activity
            android:name=".client.ClientRegistrationActivity"
            android:exported="false"
            android:parentActivityName=".RoleSelectionActivity" />

        <activity
            android:name=".superadmin.AdminRegistrationActivity"
            android:exported="false"
            android:parentActivityName=".RoleSelectionActivity" />

        <activity
            android:name=".superadmin.UserEditActivity"
            android:exported="false"
            android:parentActivityName=".superadmin.SuperadminUsersActivity" />

        <activity
            android:name=".client.ClientChatActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />

        <activity
            android:name=".client.ClientChatDetailActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientChatActivity" />

        <activity
            android:name=".AdminChatDetailActivity"
            android:exported="false"
            android:parentActivityName=".AdminChatListActivity" />

        <activity
            android:name=".AdminChatListActivity"
            android:exported="false"
            android:parentActivityName=".TourAdminMainActivity" />

        <activity
            android:name=".TourOfferDetailsActivity"
            android:exported="false"
            android:parentActivityName=".TourOffersActivity" />

        <activity
            android:name=".TourMapActivity"
            android:exported="false"
            android:parentActivityName=".GuideActiveToursActivity" />

        <activity
            android:name=".TourGuideActiveActivity"
            android:exported="false"
            android:parentActivityName=".TourGuideMainActivity" />

        <activity
            android:name=".RealTimeTrackingActivity"
            android:exported="false"
            android:parentActivityName=".client.ClientMainActivity" />

        <activity
            android:name=".ForgotPasswordActivity"
            android:exported="false"
            android:parentActivityName=".LoginActivity" />

        <activity
            android:name=".ResetPasswordActivity"
            android:exported="false"
            android:parentActivityName=".ForgotPasswordActivity" />

        <activity android:name=".client.ClientRegistrationPhotoActivity" android:exported="false" />
        <activity android:name=".client.ClientCreatePasswordActivity" android:exported="false" />
        <activity android:name=".GuideRegistrationLanguagesActivity" android:exported="false" />
        <activity android:name=".GuideRegistrationPhotoActivity" android:exported="false" />
        <activity android:name=".GuideCreatePasswordActivity" android:exported="false" />


        <!-- Actividad para mostrar estado de aprobación de guías -->
        <activity
            android:name=".GuideApprovalPendingActivity"
            android:exported="false" />

        <activity
            android:name=".UserDisabledActivity"
            android:exported="false"
            android:parentActivityName=".LoginActivity" />

        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />

        <!-- FileProvider para compartir archivos PDF -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>