<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Stop Number Badge -->
        <TextView
            android:id="@+id/tv_stop_number"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="1"
            android:gravity="center"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:background="@drawable/circle_red" />

        <!-- Stop Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_stop_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Catedral de Lima"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_stop_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Visita guiada a la catedral"
                android:textSize="13sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="4dp"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tv_stop_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Confirm Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_confirm_stop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Confirmar"
            android:textSize="12sp"
            android:layout_marginStart="8dp"
            app:cornerRadius="8dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
