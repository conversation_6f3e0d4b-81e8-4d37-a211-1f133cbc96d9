<?xml version="1.0" encoding="utf-8"?>
<!-- Archivo: res/layout/avatar_action_layout.xml -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="?attr/actionBarSize"
    android:layout_height="?attr/actionBarSize"
    android:background="?attr/selectableItemBackgroundBorderless"
    android:clickable="true"
    android:focusable="true"
    android:padding="8dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_avatar_action"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/white">

        <ImageView
            android:id="@+id/iv_avatar_action"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_avatar_24"
            android:contentDescription="Avatar del usuario" />

    </com.google.android.material.card.MaterialCardView>

</FrameLayout>