<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- Search Bar -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_search_tours"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Buscar tours..."
        app:startIconDrawable="@android:drawable/ic_menu_search"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search_tours"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Tours List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_active_tours"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/til_search_tours"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@android:drawable/ic_menu_mapmode"
            android:tint="@color/gray" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No hay tours activos"
            android:textSize="16sp"
            android:textColor="@color/gray"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Crea un tour para empezar"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
