<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with Company and Status -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_menu_info_details"
                android:tint="@color/primary"
                android:background="@drawable/circle_light_gray"
                android:padding="8dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="12dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lima Adventure Tours"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/tv_offer_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Enviado hace 2 horas"
                    android:textSize="12sp"
                    android:textColor="@color/gray"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_offer_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="PENDIENTE"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/circle_orange"
                android:padding="6dp" />

        </LinearLayout>

        <!-- Tour Information -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/bg_tour_info"
            android:padding="12dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/tv_tour_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="City Tour Lima Centro Histórico"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📅 Fecha"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="15 Dic, 2024"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏰ Hora"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="09:00 AM"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏱️ Duración"
                        android:textSize="12sp"
                        android:textColor="@color/gray" />

                    <TextView
                        android:id="@+id/tv_tour_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4 horas"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Payment Information -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💰 Pago Ofrecido"
                    android:textSize="14sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_payment_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="S/. 180.00"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/green"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👥 Participantes"
                    android:textSize="14sp"
                    android:textColor="@color/gray" />

                <TextView
                    android:id="@+id/tv_participants"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="8 personas"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Languages Required -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🗣️ Idiomas requeridos:"
            android:textSize="14sp"
            android:textColor="@color/gray"
            android:layout_marginBottom="8dp" />

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_required_languages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <com.google.android.material.chip.Chip
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Español"
                android:textSize="12sp"
                android:enabled="false" />

            <com.google.android.material.chip.Chip
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Inglés"
                android:textSize="12sp"
                android:enabled="false" />

        </com.google.android.material.chip.ChipGroup>

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/layout_pending_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:visibility="visible">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_reject_offer"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Rechazar"
                android:textColor="@color/red"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_view_details"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ver Detalles"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_accept_offer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Aceptar" />

        </LinearLayout>

        <!-- Accepted/Rejected Status (hidden by default) -->
        <LinearLayout
            android:id="@+id/layout_response_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_response_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@android:drawable/ic_menu_agenda"
                android:tint="@color/green" />

            <TextView
                android:id="@+id/tv_response_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Oferta aceptada - Tour asignado"
                android:textSize="14sp"
                android:textColor="@color/green"
                android:textStyle="bold"
                android:layout_marginStart="8dp" />

            <TextView
                android:id="@+id/tv_response_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ayer"
                android:textSize="12sp"
                android:textColor="@color/gray" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
