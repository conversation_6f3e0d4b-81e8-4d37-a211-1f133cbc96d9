<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:title="Ofertas de Tours"
        app:titleCentered="true"
        app:titleTextAppearance="@style/Toolbar.TitleText2"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_back"
        app:navigationIconTint="@color/white" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:clipToPadding="false"
        android:paddingBottom="18dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="14dp">

            <!-- Header / helper -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Gestiona tus ofertas: revisa, filtra y responde rápidamente."
                android:textSize="13sp"
                android:layout_marginHorizontal="16dp"
                android:textColor="#6B7280"
                android:layout_marginTop="2dp"
                />

            <!-- Barra de búsqueda -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_search"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Buscar una oferta o empresa"
                app:startIconDrawable="@drawable/ic_search_form"
                android:layout_margin="16dp"
                app:layout_constraintTop_toBottomOf="@id/toolbar"
                app:startIconTint="@color/primary"
                app:endIconMode="clear_text"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary"
                app:boxCornerRadiusTopStart="8dp"
                app:boxCornerRadiusTopEnd="8dp"
                app:boxCornerRadiusBottomStart="8dp"
                app:boxCornerRadiusBottomEnd="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:textSize="16sp"
                    android:textColor="#000000" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Filters Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="#ECECEC"
                android:layout_marginHorizontal="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="14dp">



                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_filter"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:singleSelection="true"
                        app:singleLine="false"
                        app:chipSpacingHorizontal="8dp"
                        app:chipSpacingVertical="8dp">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_all"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Todas"
                            android:checkable="true"
                            app:checkedIconVisible="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_pending"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Pendientes"
                            android:checkable="true"
                            android:checked="true"
                            app:checkedIconVisible="true" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_rejected"
                            style="@style/Widget.Material3.Chip.Filter"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Rechazadas"
                            android:checkable="true"
                            app:checkedIconVisible="true" />

                    </com.google.android.material.chip.ChipGroup>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- List Title -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Resultados"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#202124"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="10dp"
                android:layout_marginHorizontal="16dp"/>

            <!-- Tour Offers List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_tour_offers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                android:paddingBottom="6dp"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:layout_marginHorizontal="16dp"/>

            <LinearLayout
                android:id="@+id/empty_tour_offers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone"
                android:padding="24dp"
                android:layout_marginHorizontal="16dp">

                <ImageView
                    android:layout_width="72dp"
                    android:layout_height="72dp"
                    android:src="@drawable/ic_empty_box"
                    android:tint="#B0B0B0" />

                <TextView
                    android:id="@+id/empty_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No hay ofertas para mostrar"
                    android:textSize="14sp"
                    android:textColor="#9E9E9E"
                    android:layout_marginTop="12dp" />
            </LinearLayout>


            <!-- Bottom spacer -->
            <View
                android:layout_width="match_parent"
                android:layout_height="10dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
