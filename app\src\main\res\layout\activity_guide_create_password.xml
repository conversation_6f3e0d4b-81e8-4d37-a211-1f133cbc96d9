<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#825252">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">
        <TextView
            android:id="@+id/tvRegresar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Regresar"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:clickable="true"
            android:focusable="true"
            android:padding="8dp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="16dp">
            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_mountain"
                app:tint="#FFFFFF"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Crear Contraseña"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginStart="12dp"/>
        </LinearLayout>
    </LinearLayout>


    <!-- Contenedor central -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_gravity="bottom"

        android:background="@drawable/rounded_scrollbox_background"
        android:backgroundTint="#FFFFFF"
        android:clipToOutline="true"
        android:elevation="2dp"
        android:orientation="vertical"
        android:padding="32dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Tu contraseña debe contener:"
            android:textColor="#666666"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:text="• Mínimo 8 caracteres\n• 1 letra en minúscula\n• 1 letra en mayúscula\n• 1 número\n• 1 caracter especial"
            android:textColor="#666666"
            android:textSize="14sp" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Ingresa tu contraseña"
            app:endIconMode="password_toggle">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword"
                android:textColor="#000000"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Repite tu contraseña"
            app:endIconMode="password_toggle">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etRepeatPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword"
                android:textColor="#000000"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/tvPasswordError"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:textColor="#FF7961"
            android:textSize="14sp"
            android:visibility="gone" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSiguiente"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:backgroundTint="#2196F3"
            android:enabled="false"
            android:text="Siguiente"
            android:textAllCaps="false"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:cornerRadius="28dp"
            app:icon="@android:drawable/ic_media_play"
            app:iconGravity="end"
            app:iconTint="#FFFFFF" />
    </LinearLayout>
</LinearLayout>
